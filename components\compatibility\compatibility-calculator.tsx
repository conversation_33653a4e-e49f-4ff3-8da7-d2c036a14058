"use client"

import { useState } from "react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Button } from "@/components/ui/button"
import { compatibilityData } from "@/lib/compatibility-data"
import { zodiacData } from "@/lib/zodiac-data"
import Link from "next/link"

export function CompatibilityCalculator() {
  const [sign1, setSign1] = useState<string>("")
  const [sign2, setSign2] = useState<string>("")
  const [result, setResult] = useState<any>(null)

  // Helper function to get zodiac sign path by name
  const getSignPath = (signName: string) => {
    const sign = zodiacData.find(s => s.name === signName)
    return sign?.path || ""
  }

  const calculateCompatibility = () => {
    if (!sign1 || !sign2) return

    const key1 = `${sign1}-${sign2}`
    const key2 = `${sign2}-${sign1}`

    let compatibilityInfo

    if (compatibilityData[key1]) {
      compatibilityInfo = compatibilityData[key1]
    } else if (compatibilityData[key2]) {
      compatibilityInfo = compatibilityData[key2]
    } else {
      // Fallback for same sign or missing data
      compatibilityInfo = {
        score: sign1 === sign2 ? 75 : 50,
        shortDescription:
          sign1 === sign2
            ? `${sign1} förstår varandra på en djup nivå, men kan förstärka både positiva och negativa egenskaper.`
            : `Kompatibilitet mellan ${sign1} och ${sign2}.`,
        strengths:
          sign1 === sign2
            ? ["Djup förståelse för varandra", "Liknande värderingar och intressen", "Naturlig kommunikation"]
            : ["Olika perspektiv kan komplettera varandra", "Möjlighet till personlig tillväxt"],
        challenges:
          sign1 === sign2
            ? ["Kan förstärka varandras negativa sidor", "Kan sakna balans i relationen", "Liknande blinda fläckar"]
            : ["Kan ha olika kommunikationsstilar", "Olika prioriteringar"],
        advice:
          sign1 === sign2
            ? "Var medvetna om era gemensamma svagheter och arbeta aktivt för att balansera dem."
            : "Fokusera på att förstå varandras perspektiv och hitta gemensamma intressen.",
      }
    }

    // Calculate category scores
    const emotionalScore = Math.min(100, Math.max(30, compatibilityInfo.score + (Math.random() * 20 - 10)))
    const intellectualScore = Math.min(100, Math.max(30, compatibilityInfo.score + (Math.random() * 20 - 10)))
    const sexualScore = Math.min(100, Math.max(30, compatibilityInfo.score + (Math.random() * 20 - 10)))
    const longTermScore = Math.min(100, Math.max(30, compatibilityInfo.score + (Math.random() * 20 - 10)))

    setResult({
      ...compatibilityInfo,
      categories: {
        emotional: Math.round(emotionalScore),
        intellectual: Math.round(intellectualScore),
        sexual: Math.round(sexualScore),
        longTerm: Math.round(longTermScore),
      },
    })
  }

  const getScoreColor = (score: number) => {
    if (score >= 80) return "text-emerald-400"
    if (score >= 65) return "text-[#a78bfa]"
    if (score >= 50) return "text-amber-400"
    return "text-red-400"
  }

  const getScoreLabel = (score: number) => {
    if (score >= 80) return "Utmärkt"
    if (score >= 65) return "Bra"
    if (score >= 50) return "Neutral"
    return "Utmanande"
  }

  const getProgressColor = (score: number) => {
    if (score >= 80) return "bg-emerald-500"
    if (score >= 65) return "bg-[#a78bfa]"
    if (score >= 50) return "bg-amber-500"
    return "bg-red-500"
  }

  const getBackgroundColor = (score: number) => {
    if (score >= 80) return "bg-emerald-500/20"
    if (score >= 65) return "bg-[#a78bfa]/20"
    if (score >= 50) return "bg-amber-500/20"
    return "bg-red-500/20"
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
      <div className="space-y-6">
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-2 text-white">Ditt stjärntecken</label>
            <Select value={sign1} onValueChange={setSign1}>
              <SelectTrigger className="w-full bg-[#0c0817]/50 border-[#6e56cf]/30 text-white">
                <SelectValue placeholder="Välj ditt stjärntecken" />
              </SelectTrigger>
              <SelectContent className="bg-[#1a1333] border-[#6e56cf]/30">
                {zodiacData.map((sign) => (
                  <SelectItem key={sign.name} value={sign.name} className="text-white hover:bg-[#6e56cf]/20">
                    {sign.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <label className="block text-sm font-medium mb-2 text-white">Partners stjärntecken</label>
            <Select value={sign2} onValueChange={setSign2}>
              <SelectTrigger className="w-full bg-[#0c0817]/50 border-[#6e56cf]/30 text-white">
                <SelectValue placeholder="Välj partners stjärntecken" />
              </SelectTrigger>
              <SelectContent className="bg-[#1a1333] border-[#6e56cf]/30">
                {zodiacData.map((sign) => (
                  <SelectItem key={sign.name} value={sign.name} className="text-white hover:bg-[#6e56cf]/20">
                    {sign.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <Button
            onClick={calculateCompatibility}
            disabled={!sign1 || !sign2}
            className="w-full bg-gradient-to-r from-[#6e56cf]/80 to-[#a78bfa]/80 hover:from-[#6e56cf]/90 hover:to-[#a78bfa]/90 text-white font-medium border border-[#a78bfa]/30 shadow-md hover:shadow-lg transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Beräkna kompatibilitet
          </Button>
        </div>

        {result && (
          <div className="space-y-6">
            <div className="text-center">
              <div className={`relative inline-block w-40 h-40 rounded-full p-4 ${getBackgroundColor(result.score)}`}>
                <svg className="w-full h-full" viewBox="0 0 100 100">
                  <circle
                    cx="50"
                    cy="50"
                    r="45"
                    fill="none"
                    stroke="currentColor"
                    className="text-slate-600/30"
                    strokeWidth="8"
                  />
                  <circle
                    cx="50"
                    cy="50"
                    r="45"
                    fill="none"
                    stroke="currentColor"
                    className={getScoreColor(result.score)}
                    strokeWidth="8"
                    strokeDasharray={`${result.score * 2.83} 283`}
                    strokeLinecap="round"
                    transform="rotate(-90 50 50)"
                  />
                </svg>
                <div className="absolute inset-0 flex items-center justify-center flex-col">
                  <span className={`text-4xl font-bold ${getScoreColor(result.score)}`}>{result.score}</span>
                  <span className="text-sm text-slate-300">av 100</span>
                </div>
              </div>
              <p className={`text-xl font-medium mt-4 ${getScoreColor(result.score)}`}>{getScoreLabel(result.score)}</p>
            </div>

            <div className="space-y-4">
              <div className="bg-[#0c0817]/30 rounded-lg p-4">
                <div className="flex justify-between mb-2">
                  <span className="text-sm font-medium text-white">Emotionell kompatibilitet</span>
                  <span className={`text-sm font-bold ${getScoreColor(result.categories.emotional)}`}>
                    {result.categories.emotional}%
                  </span>
                </div>
                <div className="w-full bg-slate-600/30 rounded-full h-3">
                  <div
                    className={`${getProgressColor(result.categories.emotional)} h-3 rounded-full transition-all duration-500 ease-out`}
                    style={{ width: `${result.categories.emotional}%` }}
                  ></div>
                </div>
              </div>

              <div className="bg-[#0c0817]/30 rounded-lg p-4">
                <div className="flex justify-between mb-2">
                  <span className="text-sm font-medium text-white">Intellektuell kompatibilitet</span>
                  <span className={`text-sm font-bold ${getScoreColor(result.categories.intellectual)}`}>
                    {result.categories.intellectual}%
                  </span>
                </div>
                <div className="w-full bg-slate-600/30 rounded-full h-3">
                  <div
                    className={`${getProgressColor(result.categories.intellectual)} h-3 rounded-full transition-all duration-500 ease-out`}
                    style={{ width: `${result.categories.intellectual}%` }}
                  ></div>
                </div>
              </div>

              <div className="bg-[#0c0817]/30 rounded-lg p-4">
                <div className="flex justify-between mb-2">
                  <span className="text-sm font-medium text-white">Sexuell kompatibilitet</span>
                  <span className={`text-sm font-bold ${getScoreColor(result.categories.sexual)}`}>
                    {result.categories.sexual}%
                  </span>
                </div>
                <div className="w-full bg-slate-600/30 rounded-full h-3">
                  <div
                    className={`${getProgressColor(result.categories.sexual)} h-3 rounded-full transition-all duration-500 ease-out`}
                    style={{ width: `${result.categories.sexual}%` }}
                  ></div>
                </div>
              </div>

              <div className="bg-[#0c0817]/30 rounded-lg p-4">
                <div className="flex justify-between mb-2">
                  <span className="text-sm font-medium text-white">Långsiktig kompatibilitet</span>
                  <span className={`text-sm font-bold ${getScoreColor(result.categories.longTerm)}`}>
                    {result.categories.longTerm}%
                  </span>
                </div>
                <div className="w-full bg-slate-600/30 rounded-full h-3">
                  <div
                    className={`${getProgressColor(result.categories.longTerm)} h-3 rounded-full transition-all duration-500 ease-out`}
                    style={{ width: `${result.categories.longTerm}%` }}
                  ></div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {result ? (
        <div className="space-y-6">
          <div className="bg-[#0c0817]/30 rounded-xl p-6 border border-[#6e56cf]/20">
            <h3 className="text-xl font-display mb-4 text-white">
              {sign1} + {sign2}
            </h3>
            <p className="text-slate-300 mb-6 leading-relaxed">{result.shortDescription}</p>

            {/* Link to detailed compatibility page */}
            <Link
              href={`/relationer/${getSignPath(sign1)}/${getSignPath(sign2)}`}
              className="inline-block py-3 px-6 bg-gradient-to-r from-[#6e56cf]/80 to-[#a78bfa]/80 hover:from-[#6e56cf]/90 hover:to-[#a78bfa]/90 text-white font-medium rounded-lg border border-[#a78bfa]/30 shadow-md hover:shadow-lg transition-all duration-300"
            >
              Läs fullständig analys →
            </Link>
          </div>

          <div className="bg-[#0c0817]/30 rounded-xl p-6 border border-[#6e56cf]/20">
            <h4 className="font-medium text-emerald-400 mb-3 flex items-center">
              <span className="mr-2">✓</span> Styrkor
            </h4>
            <ul className="space-y-2 mb-4">
              {result.strengths.map((strength: string, index: number) => (
                <li key={index} className="text-slate-300 flex items-start">
                  <span className="text-emerald-400 mr-2 mt-1">•</span>
                  {strength}
                </li>
              ))}
            </ul>
          </div>

          <div className="bg-[#0c0817]/30 rounded-xl p-6 border border-[#6e56cf]/20">
            <h4 className="font-medium text-amber-400 mb-3 flex items-center">
              <span className="mr-2">⚠</span> Utmaningar
            </h4>
            <ul className="space-y-2 mb-4">
              {result.challenges.map((challenge: string, index: number) => (
                <li key={index} className="text-slate-300 flex items-start">
                  <span className="text-amber-400 mr-2 mt-1">•</span>
                  {challenge}
                </li>
              ))}
            </ul>
          </div>

          <div className="bg-[#0c0817]/30 rounded-xl p-6 border border-[#6e56cf]/20">
            <h4 className="font-medium text-[#a78bfa] mb-3 flex items-center">
              <span className="mr-2">💡</span> Råd för relationen
            </h4>
            <p className="text-slate-300 leading-relaxed">{result.advice}</p>
          </div>
        </div>
      ) : (
        <div className="flex items-center justify-center h-full">
          <div className="text-center p-8 border border-dashed border-[#6e56cf]/30 rounded-xl bg-[#0c0817]/20 w-full">
            <div className="mb-4">
              <span className="text-4xl">✨</span>
            </div>
            <p className="text-slate-300 mb-3 text-lg">
              Välj två stjärntecken för att se detaljerad kompatibilitetsanalys
            </p>
            <p className="text-sm text-slate-400">
              Du får information om styrkor, utmaningar, råd för relationen och en länk till fullständig analys
            </p>
          </div>
        </div>
      )}
    </div>
  )
}
