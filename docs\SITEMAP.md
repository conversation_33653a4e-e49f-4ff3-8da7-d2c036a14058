# Automatisk Sitemap-hantering

Detta projekt har ett automatiskt sitemap-system som uppdateras när nya artiklar läggs till.

## Hur det fungerar

### 1. Automatisk generering
- **Fil**: `app/sitemap.ts`
- **URL**: `https://horoskopet.nu/sitemap.xml`
- Sitemap genereras automatiskt av Next.js när sidan byggs eller när `/sitemap.xml` besöks
- Skannar alla `page.tsx` filer i `app/`-mappen rekursivt
- Lägger automatiskt till alla dynamiska routes (stjärntecken, horoskop, kompatibilitet)

### 2. Robots.txt
- **Fil**: `app/robots.ts`
- **URL**: `https://horoskopet.nu/robots.txt`
- Pekar sökmotorer till sitemap-filen
- Konfigurerar vilka delar av webbplatsen som får indexeras

### 3. Utility-funktioner
- **Fil**: `lib/sitemap-utils.ts`
- Hjälpfunktioner för sitemap-hantering
- Automatisk prioritering och uppdateringsfrekvens
- Validering av vilka sidor som ska inkluderas

## Automatiska funktioner

### Prioritering
Sidor får automatiskt prioritet baserat på typ:
- **Startsida**: 1.0
- **Dagliga/veckans/månadens horoskop**: 0.9
- **Stjärntecken**: 0.8
- **Horoskop-sektioner**: 0.8
- **Astrologi-lära**: 0.7
- **Relationer**: 0.7
- **Aktuellt**: 0.6
- **Övriga sidor**: 0.5

### Uppdateringsfrekvens
- **Dagens horoskop**: daily
- **Veckans horoskop**: weekly
- **Månadens horoskop**: monthly
- **Aktuellt**: weekly
- **Startsida**: daily
- **Övriga horoskop**: weekly
- **Alla andra**: monthly

### Automatisk inkludering
Systemet inkluderar automatiskt:
- ✅ Alla statiska sidor med `page.tsx`
- ✅ Alla 12 stjärnteckens-sidor
- ✅ Alla dagliga horoskop-sidor (12 st)
- ✅ Alla veckans horoskop-sidor (12 st)
- ✅ Alla månadens horoskop-sidor (12 st)
- ✅ Alla relations-sidor (12 st)
- ✅ Alla kompatibilitetssidor (144 st = 12×12)

### Automatisk exkludering
Systemet exkluderar automatiskt:
- ❌ Dynamiska routes med `[brackets]`
- ❌ API-routes (`/api/`)
- ❌ Admin-sidor
- ❌ Error-sidor (404, 500)
- ❌ HTML sitemap-sidan
- ❌ **Redirect-sidor** (sidor som använder `redirect()` från Next.js)

## Manuell uppdatering

### När nya artiklar läggs till:

```bash
# Kör sitemap-uppdatering
npm run update-sitemap

# Eller kort version
npm run sitemap

# Kontrollera redirect-sidor
node scripts/check-redirects.js
```

### Vad händer:
1. Skannar alla nya `page.tsx` filer
2. Loggar ändringar i `logs/sitemap.log`
3. Visar statistik över antal sidor
4. Nästa gång `/sitemap.xml` besöks genereras den nya sitemap

## Loggning

Alla sitemap-uppdateringar loggas i:
- **Fil**: `logs/sitemap.log`
- **Format**: `[timestamp] Sitemap uppdaterad med X sidor`

## Exempel på användning

### Lägg till ny artikel
1. Skapa ny `page.tsx` fil, t.ex. `app/aktuellt/ny-artikel/page.tsx`
2. Kör `npm run update-sitemap`
3. Sitemap uppdateras automatiskt nästa gång den genereras

### Lägg till ny sektion
1. Skapa ny mapp med `page.tsx`, t.ex. `app/ny-sektion/page.tsx`
2. Kör `npm run update-sitemap`
3. Justera prioritet i `lib/sitemap-utils.ts` om nödvändigt

## Tekniska detaljer

### Next.js 13+ Sitemap API
- Använder Next.js inbyggda `MetadataRoute.Sitemap`
- Genereras vid build-time och on-demand
- Automatisk caching och optimering

### Filstruktur
```
app/
├── sitemap.ts          # Huvudfil för sitemap-generering
├── robots.ts           # Robots.txt konfiguration
lib/
├── sitemap-utils.ts    # Utility-funktioner
scripts/
├── update-sitemap.js   # Manuellt uppdateringsscript
docs/
├── SITEMAP.md         # Denna dokumentation
logs/
├── sitemap.log        # Loggfil för ändringar
```

## Felsökning

### Sitemap uppdateras inte
1. Kontrollera att `page.tsx` filen finns
2. Kör `npm run update-sitemap` för att se om sidan hittas
3. Kontrollera att sidan inte exkluderas av filter-funktionerna

### Fel prioritet eller uppdateringsfrekvens
1. Redigera `getAutoPriority()` eller `getAutoChangeFrequency()` i `lib/sitemap-utils.ts`
2. Kör `npm run update-sitemap`

### Dynamiska routes saknas
1. Lägg till dem manuellt i `app/sitemap.ts`
2. Eller utöka `generateHoroscopePages()` funktionen

## SEO-fördelar

✅ **Automatisk upptäckt**: Sökmotorer hittar nya sidor automatiskt
✅ **Korrekt prioritering**: Viktiga sidor får högre prioritet
✅ **Uppdateringsfrekvens**: Sökmotorer vet när de ska återbesöka
✅ **Komplett täckning**: Alla sidor inkluderas automatiskt
✅ **Robots.txt integration**: Pekar sökmotorer till sitemap

## Redirect-hantering

### Problem med 3XX redirects i sitemap
Tidigare inkluderade sitemap:en sidor som använder `redirect()` funktionen från Next.js. Detta skapade 3XX redirects som Ahrefs och andra SEO-verktyg flaggade som problem.

### Lösning
Systemet identifierar nu automatiskt redirect-sidor och exkluderar dem från sitemap:en. Endast destination-URL:erna inkluderas.

### Identifierade redirect-sidor
Följande 9 redirect-sidor exkluderas automatiskt:

1. `/astrologi-lara/aspekter/gyllene-yod` → `/astrologi-lara/aspekter/monster/gyllene-yod`
2. `/astrologi-lara/aspekter/t-kvadratur` → `/astrologi-lara/aspekter/monster/t-kvadratur`
3. `/astrologi-lara/aspekter/thor-hammare` → `/astrologi-lara/aspekter/monster/thors-hammare`
4. `/astrologi-lara/aspekter/yod` → `/astrologi-lara/aspekter/monster/yod`
5. `/astrologi-lara/husen/fjärde-huset` → `/astrologi-lara/husen/fjarde-huset`
6. `/astrologi-lara/husen/första-huset` → `/astrologi-lara/husen/forsta-huset`
7. `/astrologi-lara/husen/sjätte-huset` → `/astrologi-lara/husen/sjatte-huset`
8. `/astrologi-lara/husen/åttonde-huset` → `/astrologi-lara/husen/attonde-huset`
9. `/astrologi-lara/planeter/månen` → `/astrologi-lara/planeter/manen`

### Kontrollera redirect-sidor
```bash
node scripts/check-redirects.js
```

Detta script visar alla redirect-sidor och vart de pekar.

## Underhåll

Systemet kräver minimal underhåll:
- Kör `npm run update-sitemap` när nya artiklar läggs till
- Kontrollera `logs/sitemap.log` regelbundet
- Uppdatera prioriteringar vid behov i `lib/sitemap-utils.ts`
