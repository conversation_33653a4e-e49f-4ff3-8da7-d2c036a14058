"use client"

import { useState, useEffect, useRef } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { <PERSON>lider } from "@/components/ui/slider"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { <PERSON><PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"

// Typer för aspektmönster och planeter
type AspectPattern = {
  id: string
  name: string
  description: string
  planets: Planet[]
  aspects: Aspect[]
}

type Planet = {
  id: string
  name: string
  symbol: string
  position: number // Position i grader (0-360)
  color: string
}

type Aspect = {
  from: string
  to: string
  type: "conjunction" | "opposition" | "trine" | "square" | "sextile" | "quincunx"
  color: string
}

// Aspektmönster data
const aspectPatterns: AspectPattern[] = [
  {
    id: "grand-trine",
    name: "<PERSON><PERSON><PERSON><PERSON>",
    description: "Tre planeter i trigon (120°) till varandra, bildar en liksidig triangel.",
    planets: [
      { id: "p1", name: "Jupiter", symbol: "♃", position: 0, color: "#E67E22" },
      { id: "p2", name: "Saturnus", symbol: "♄", position: 120, color: "#7F8C8D" },
      { id: "p3", name: "Neptunus", symbol: "♆", position: 240, color: "#3498DB" },
    ],
    aspects: [
      { from: "p1", to: "p2", type: "trine", color: "#2ECC71" },
      { from: "p2", to: "p3", type: "trine", color: "#2ECC71" },
      { from: "p3", to: "p1", type: "trine", color: "#2ECC71" },
    ],
  },
  {
    id: "t-square",
    name: "T-kvadratur",
    description: "Två planeter i opposition (180°) och båda i kvadratur (90°) till en tredje planet.",
    planets: [
      { id: "p1", name: "Mars", symbol: "♂", position: 0, color: "#E74C3C" },
      { id: "p2", name: "Pluto", symbol: "♇", position: 180, color: "#8E44AD" },
      { id: "p3", name: "Uranus", symbol: "♅", position: 90, color: "#3498DB" },
    ],
    aspects: [
      { from: "p1", to: "p2", type: "opposition", color: "#E74C3C" },
      { from: "p1", to: "p3", type: "square", color: "#E67E22" },
      { from: "p2", to: "p3", type: "square", color: "#E67E22" },
    ],
  },
  {
    id: "grand-cross",
    name: "Grand Cross",
    description: "Fyra planeter i kvadratur (90°) till varandra, bildar ett kors.",
    planets: [
      { id: "p1", name: "Mars", symbol: "♂", position: 0, color: "#E74C3C" },
      { id: "p2", name: "Venus", symbol: "♀", position: 90, color: "#1ABC9C" },
      { id: "p3", name: "Jupiter", symbol: "♃", position: 180, color: "#E67E22" },
      { id: "p4", name: "Saturnus", symbol: "♄", position: 270, color: "#7F8C8D" },
    ],
    aspects: [
      { from: "p1", to: "p2", type: "square", color: "#E67E22" },
      { from: "p2", to: "p3", type: "square", color: "#E67E22" },
      { from: "p3", to: "p4", type: "square", color: "#E67E22" },
      { from: "p4", to: "p1", type: "square", color: "#E67E22" },
      { from: "p1", to: "p3", type: "opposition", color: "#E74C3C" },
      { from: "p2", to: "p4", type: "opposition", color: "#E74C3C" },
    ],
  },
  {
    id: "yod",
    name: "Yod",
    description: "Två planeter i sextil (60°) till varandra och båda i kvinkuns (150°) till en tredje planet.",
    planets: [
      { id: "p1", name: "Merkurius", symbol: "☿", position: 0, color: "#3498DB" },
      { id: "p2", name: "Venus", symbol: "♀", position: 60, color: "#1ABC9C" },
      { id: "p3", name: "Pluto", symbol: "♇", position: 210, color: "#8E44AD" },
    ],
    aspects: [
      { from: "p1", to: "p2", type: "sextile", color: "#3498DB" },
      { from: "p1", to: "p3", type: "quincunx", color: "#9B59B6" },
      { from: "p2", to: "p3", type: "quincunx", color: "#9B59B6" },
    ],
  },
  {
    id: "kite",
    name: "Kite",
    description:
      "Ett stortrigon med en fjärde planet i opposition till en av planeterna och i sextil till de andra två.",
    planets: [
      { id: "p1", name: "Solen", symbol: "☉", position: 0, color: "#F1C40F" },
      { id: "p2", name: "Jupiter", symbol: "♃", position: 120, color: "#E67E22" },
      { id: "p3", name: "Neptunus", symbol: "♆", position: 240, color: "#3498DB" },
      { id: "p4", name: "Saturnus", symbol: "♄", position: 180, color: "#7F8C8D" },
    ],
    aspects: [
      { from: "p1", to: "p2", type: "trine", color: "#2ECC71" },
      { from: "p2", to: "p3", type: "trine", color: "#2ECC71" },
      { from: "p3", to: "p1", type: "trine", color: "#2ECC71" },
      { from: "p1", to: "p4", type: "opposition", color: "#E74C3C" },
      { from: "p2", to: "p4", type: "sextile", color: "#3498DB" },
      { from: "p3", to: "p4", type: "sextile", color: "#3498DB" },
    ],
  },
  {
    id: "mystic-rectangle",
    name: "Mystiska Rektangeln",
    description:
      "Två par av planeter i opposition (180°) till varandra, med sextiler (60°) och trigoner (120°) mellan dem.",
    planets: [
      { id: "p1", name: "Venus", symbol: "♀", position: 0, color: "#1ABC9C" },
      { id: "p2", name: "Mars", symbol: "♂", position: 180, color: "#E74C3C" },
      { id: "p3", name: "Jupiter", symbol: "♃", position: 60, color: "#E67E22" },
      { id: "p4", name: "Saturnus", symbol: "♄", position: 240, color: "#7F8C8D" },
    ],
    aspects: [
      { from: "p1", to: "p2", type: "opposition", color: "#E74C3C" },
      { from: "p3", to: "p4", type: "opposition", color: "#E74C3C" },
      { from: "p1", to: "p3", type: "sextile", color: "#3498DB" },
      { from: "p2", to: "p4", type: "sextile", color: "#3498DB" },
      { from: "p1", to: "p4", type: "trine", color: "#2ECC71" },
      { from: "p2", to: "p3", type: "trine", color: "#2ECC71" },
    ],
  },
  {
    id: "stellium",
    name: "Stellium",
    description: "Tre eller fler planeter i konjunktion (0-10°) med varandra.",
    planets: [
      { id: "p1", name: "Solen", symbol: "☉", position: 0, color: "#F1C40F" },
      { id: "p2", name: "Merkurius", symbol: "☿", position: 5, color: "#3498DB" },
      { id: "p3", name: "Venus", symbol: "♀", position: 8, color: "#1ABC9C" },
      { id: "p4", name: "Mars", symbol: "♂", position: 12, color: "#E74C3C" },
    ],
    aspects: [
      { from: "p1", to: "p2", type: "conjunction", color: "#F1C40F" },
      { from: "p1", to: "p3", type: "conjunction", color: "#F1C40F" },
      { from: "p1", to: "p4", type: "conjunction", color: "#F1C40F" },
      { from: "p2", to: "p3", type: "conjunction", color: "#F1C40F" },
      { from: "p2", to: "p4", type: "conjunction", color: "#F1C40F" },
      { from: "p3", to: "p4", type: "conjunction", color: "#F1C40F" },
    ],
  },
]

// Hjälpfunktioner för att beräkna koordinater
const polarToCartesian = (centerX: number, centerY: number, radius: number, angleInDegrees: number) => {
  const angleInRadians = ((angleInDegrees - 90) * Math.PI) / 180.0
  return {
    x: centerX + radius * Math.cos(angleInRadians),
    y: centerY + radius * Math.sin(angleInRadians),
  }
}

export function AspectPatternVisualizer() {
  const [selectedPattern, setSelectedPattern] = useState<AspectPattern>(aspectPatterns[0])
  const [rotation, setRotation] = useState(0)
  const [zoom, setZoom] = useState(1)
  const [showLabels, setShowLabels] = useState(true)
  const [showAspects, setShowAspects] = useState(true)
  const [showSymbols, setShowSymbols] = useState(true)
  const [activeTab, setActiveTab] = useState("visual")
  const svgRef = useRef<SVGSVGElement>(null)

  // Konstanter för SVG-dimensioner
  const svgWidth = 500
  const svgHeight = 500
  const centerX = svgWidth / 2
  const centerY = svgHeight / 2
  const baseRadius = Math.min(svgWidth, svgHeight) * 0.4 // Basradie för zodiakhjulet

  // Effekt för att hantera fönsterändring
  useEffect(() => {
    const handleResize = () => {
      // Uppdatera visualiseringen vid behov
    }

    window.addEventListener("resize", handleResize)
    return () => window.removeEventListener("resize", handleResize)
  }, [])

  // Funktion för att rita aspektlinjer
  const renderAspects = () => {
    if (!showAspects) return null

    return selectedPattern.aspects.map((aspect, index) => {
      const fromPlanet = selectedPattern.planets.find((p) => p.id === aspect.from)
      const toPlanet = selectedPattern.planets.find((p) => p.id === aspect.to)

      if (!fromPlanet || !toPlanet) return null

      const fromPos = polarToCartesian(centerX, centerY, baseRadius * zoom, fromPlanet.position + rotation)
      const toPos = polarToCartesian(centerX, centerY, baseRadius * zoom, toPlanet.position + rotation)

      // Olika linjestilar för olika aspekttyper
      let strokeDasharray = ""
      let strokeWidth = 2

      switch (aspect.type) {
        case "conjunction":
          strokeWidth = 3
          break
        case "opposition":
          strokeWidth = 2.5
          break
        case "trine":
          strokeWidth = 2
          break
        case "square":
          strokeWidth = 2
          break
        case "sextile":
          strokeDasharray = "5,3"
          break
        case "quincunx":
          strokeDasharray = "2,2"
          break
      }

      return (
        <line
          key={`aspect-${index}`}
          x1={fromPos.x}
          y1={fromPos.y}
          x2={toPos.x}
          y2={toPos.y}
          stroke={aspect.color}
          strokeWidth={strokeWidth}
          strokeDasharray={strokeDasharray}
          opacity={0.7}
        />
      )
    })
  }

  // Funktion för att rita planeter
  const renderPlanets = () => {
    return selectedPattern.planets.map((planet) => {
      const pos = polarToCartesian(centerX, centerY, baseRadius * zoom, planet.position + rotation)

      return (
        <g key={planet.id} transform={`translate(${pos.x}, ${pos.y})`}>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <circle r={12} fill={planet.color} stroke="#fff" strokeWidth={1.5} className="cursor-pointer" />
              </TooltipTrigger>
              <TooltipContent>
                <p className="font-bold">{planet.name}</p>
                <p>Position: {planet.position}°</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          {showSymbols && (
            <text textAnchor="middle" dominantBaseline="middle" fill="white" fontSize="14" fontWeight="bold">
              {planet.symbol}
            </text>
          )}

          {showLabels && (
            <text
              textAnchor="middle"
              dominantBaseline="middle"
              fill={planet.color}
              fontSize="12"
              fontWeight="bold"
              y={24}
              stroke="#fff"
              strokeWidth={0.5}
              paintOrder="stroke"
            >
              {planet.name}
            </text>
          )}
        </g>
      )
    })
  }

  // Funktion för att rita zodiakhjulet
  const renderZodiacWheel = () => {
    return (
      <g transform={`rotate(${rotation}, ${centerX}, ${centerY})`}>
        <circle
          cx={centerX}
          cy={centerY}
          r={baseRadius * zoom}
          fill="none"
          stroke="#ccc"
          strokeWidth={1}
          opacity={0.5}
        />
      </g>
    )
  }

  // Hantera mönsterval
  const handlePatternChange = (value: string) => {
    const pattern = aspectPatterns.find((p) => p.id === value)
    if (pattern) {
      setSelectedPattern(pattern)
    }
  }

  return (
    <div className="w-full max-w-4xl mx-auto bg-white rounded-lg shadow-md overflow-hidden">
      <div className="p-4 border-b">
        <h2 className="text-2xl font-bold">Interaktiv visualisering av aspektmönster</h2>
        <p className="text-muted-foreground">
          Utforska de olika geometriska mönstren som planeter kan bilda i ett horoskop.
        </p>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <div className="px-4 pt-4">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="visual">Visualisering</TabsTrigger>
            <TabsTrigger value="info">Information</TabsTrigger>
          </TabsList>
        </div>

        <TabsContent value="visual" className="p-4">
          <div className="grid md:grid-cols-[1fr_300px] gap-4">
            <div className="bg-slate-50 rounded-lg p-4 flex justify-center items-center">
              <svg
                ref={svgRef}
                width={svgWidth}
                height={svgHeight}
                viewBox={`0 0 ${svgWidth} ${svgHeight}`}
                className="max-w-full h-auto"
              >
                {renderZodiacWheel()}
                {renderAspects()}
                {renderPlanets()}
              </svg>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-1">Välj aspektmönster</label>
                <Select value={selectedPattern.id} onValueChange={handlePatternChange}>
                  <SelectTrigger>
                    <SelectValue placeholder="Välj aspektmönster" />
                  </SelectTrigger>
                  <SelectContent>
                    {aspectPatterns.map((pattern) => (
                      <SelectItem key={pattern.id} value={pattern.id}>
                        {pattern.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">Rotation: {rotation}°</label>
                <Slider
                  value={[rotation]}
                  min={0}
                  max={360}
                  step={1}
                  onValueChange={(value) => setRotation(value[0])}
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">Zoom: {zoom.toFixed(1)}x</label>
                <Slider value={[zoom]} min={0.5} max={1.5} step={0.1} onValueChange={(value) => setZoom(value[0])} />
              </div>

              <div className="flex flex-wrap gap-2">
                <Button
                  variant={showLabels ? "default" : "outline"}
                  size="sm"
                  onClick={() => setShowLabels(!showLabels)}
                >
                  {showLabels ? "Dölj namn" : "Visa namn"}
                </Button>
                <Button
                  variant={showSymbols ? "default" : "outline"}
                  size="sm"
                  onClick={() => setShowSymbols(!showSymbols)}
                >
                  {showSymbols ? "Dölj symboler" : "Visa symboler"}
                </Button>
                <Button
                  variant={showAspects ? "default" : "outline"}
                  size="sm"
                  onClick={() => setShowAspects(!showAspects)}
                >
                  {showAspects ? "Dölj aspekter" : "Visa aspekter"}
                </Button>
              </div>

              <div className="p-3 bg-slate-50 rounded-md">
                <h3 className="font-medium mb-1">{selectedPattern.name}</h3>
                <p className="text-sm text-muted-foreground">{selectedPattern.description}</p>
              </div>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="info" className="p-4">
          <div className="prose max-w-none">
            <h3>Om aspektmönster</h3>
            <p>
              Aspektmönster är specifika konfigurationer av planeter som bildar geometriska mönster i ett horoskop.
              Dessa mönster ger viktiga insikter om en persons psykologiska dynamik, talanger, utmaningar och livsväg.
            </p>

            <h4>Vanliga aspektmönster</h4>
            <ul>
              <li>
                <strong>Stortrigon (Grand Trine)</strong> - Tre planeter i trigon (120°) till varandra, bildar en
                liksidig triangel. Representerar naturligt flöde, harmoni och talang.
              </li>
              <li>
                <strong>T-kvadratur</strong> - Två planeter i opposition (180°) och båda i kvadratur (90°) till en
                tredje planet. Representerar spänning och utmaning som driver till handling.
              </li>
              <li>
                <strong>Grand Cross</strong> - Fyra planeter i kvadratur (90°) till varandra, bildar ett kors.
                Representerar intensiv spänning och dynamisk energi som kräver balans.
              </li>
              <li>
                <strong>Yod (Ödesfingret)</strong> - Två planeter i sextil (60°) till varandra och båda i kvinkuns
                (150°) till en tredje planet. Representerar en karmisk uppgift eller speciellt syfte.
              </li>
              <li>
                <strong>Kite</strong> - Ett stortrigon med en fjärde planet i opposition till en av planeterna och i
                sextil till de andra två. Representerar talang med riktning och fokus.
              </li>
              <li>
                <strong>Mystiska Rektangeln</strong> - Två par av planeter i opposition (180°) till varandra, med
                sextiler (60°) och trigoner (120°) mellan dem. Representerar balans mellan spänning och harmoni.
              </li>
              <li>
                <strong>Stellium</strong> - Tre eller fler planeter i konjunktion (0-10°) med varandra. Representerar
                intensiv koncentration av energi inom ett specifikt område.
              </li>
            </ul>

            <h4>Tolkning av aspektmönster</h4>
            <p>När du tolkar aspektmönster, ta hänsyn till:</p>
            <ul>
              <li>Vilka planeter som är involverade och deras symboliska betydelse</li>
              <li>I vilka tecken och hus planeterna befinner sig</li>
              <li>Hur exakta (tight) aspekterna är</li>
              <li>Om det finns andra aspekter som modifierar mönstret</li>
              <li>Hur mönstret relaterar till resten av horoskopet</li>
            </ul>

            <p>
              Aspektmönster ger en djupare dimension till horoskoptolkningen och hjälper till att identifiera viktiga
              teman i en persons liv.
            </p>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
