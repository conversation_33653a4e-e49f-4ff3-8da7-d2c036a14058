@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 260 50% 97%;
    --foreground: 260 10% 10%;
    --card: 260 30% 99%;
    --card-foreground: 260 10% 10%;
    --popover: 260 30% 99%;
    --popover-foreground: 260 10% 10%;
    --primary: 260 60% 50%;
    --primary-foreground: 260 10% 98%;
    --secondary: 240 30% 60%;
    --secondary-foreground: 240 10% 98%;
    --muted: 260 20% 90%;
    --muted-foreground: 260 10% 40%;
    --accent: 280 50% 50%;
    --accent-foreground: 280 10% 98%;
    --destructive: 0 100% 50%;
    --destructive-foreground: 0 0% 100%;
    --border: 260 20% 85%;
    --input: 260 20% 80%;
    --ring: 260 60% 50%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 260 50% 5%;
    --foreground: 260 10% 99%;
    --card: 260 30% 10%;
    --card-foreground: 260 10% 99%;
    --popover: 260 30% 10%;
    --popover-foreground: 260 10% 98%;
    --primary: 260 60% 50%;
    --primary-foreground: 260 10% 98%;
    --secondary: 240 30% 30%;
    --secondary-foreground: 240 10% 98%;
    --muted: 260 20% 20%;
    --muted-foreground: 260 10% 70%;
    --accent: 280 50% 40%;
    --accent-foreground: 280 10% 98%;
    --destructive: 0 100% 50%;
    --destructive-foreground: 0 0% 100%;
    --border: 260 20% 30%;
    --input: 260 20% 30%;
    --ring: 260 60% 50%;
  }

  * {
    @apply border-border;
  }
  
  body {
    @apply bg-background text-foreground;
    background: linear-gradient(135deg, #1a1333 0%, #221a44 40%, #2d1d57 100%);
    background-attachment: fixed;
    position: relative;
  }
  
  body::before {
    content: "";
    position: fixed;
    inset: 0;
    z-index: 0;
    pointer-events: none;
    background-image:
      radial-gradient(1px 1px at 25% 25%, rgba(255,255,255,0.08) 100%, transparent),
      radial-gradient(1px 1px at 50% 50%, rgba(255,255,255,0.08) 100%, transparent),
      radial-gradient(2px 2px at 75% 75%, rgba(255,255,255,0.10) 100%, transparent),
      radial-gradient(2px 2px at 100% 50%, rgba(255,255,255,0.07) 100%, transparent),
      radial-gradient(1px 1px at 22% 65%, rgba(255,255,255,0.07) 100%, transparent),
      radial-gradient(1px 1px at 35% 12%, rgba(255,255,255,0.08) 100%, transparent),
      radial-gradient(1px 1px at 85% 32%, rgba(255,255,255,0.07) 100%, transparent),
      radial-gradient(1px 1px at 10% 80%, rgba(255,255,255,0.09) 100%, transparent),
      radial-gradient(1px 1px at 65% 92%, rgba(255,255,255,0.08) 100%, transparent);
    background-size: 550px 550px;
    animation: stars-move 100s linear infinite;
    opacity: 0.4;
    mix-blend-mode: lighten;
    transition: opacity 0.4s;
    will-change: opacity;
  }
  
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    @apply font-display font-bold tracking-wide;
    letter-spacing: 0.02em;
  }

  h1 {
    @apply text-4xl md:text-5xl;
  }

  h2 {
    @apply text-3xl md:text-4xl;
  }

  h3 {
    @apply text-2xl md:text-3xl;
  }

  p,
  li,
  span,
  div {
    @apply font-sans;
  }

  /* Stjärnhimmel bakgrund */
  .stars-bg {
    @apply relative overflow-hidden;
  }

  .stars-bg::before {
    content: "";
    @apply absolute inset-0 bg-stars-pattern opacity-30 z-0;
    animation: twinkling 8s infinite linear;
  }

  @keyframes twinkling {
    0% {
      opacity: 0.2;
    }
    50% {
      opacity: 0.5;
    }
    100% {
      opacity: 0.2;
    }
  }

  /* Fullscreen video hero */
  .video-hero {
    @apply relative w-full h-screen overflow-hidden;
  }

  .video-hero video {
    @apply absolute top-0 left-0 w-full h-full object-cover;
  }

  .video-hero-content {
    @apply relative z-10 flex flex-col items-center justify-center h-full text-white text-center px-4;
    background: linear-gradient(to bottom, rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.7));
  }

  /* Förbättrad typografi */
  .serif-text {
    @apply font-serif;
  }

  .quote {
    @apply font-serif italic text-lg md:text-xl text-cosmic-300 border-l-4 border-cosmic-500 pl-4 my-6;
  }

  .cosmic-title {
    @apply font-display text-transparent bg-clip-text bg-gradient-to-r from-cosmic-400 to-stardust-500;
  }

  /* Small stars background for navbar */
  .stars-small {
    background-image: radial-gradient(1px 1px at 25% 25%, rgba(255, 255, 255, 0.6) 100%, transparent),
      radial-gradient(1px 1px at 50% 50%, rgba(255, 255, 255, 0.6) 100%, transparent),
      radial-gradient(2px 2px at 75% 75%, rgba(255, 255, 255, 0.6) 100%, transparent),
      radial-gradient(2px 2px at 100% 50%, rgba(255, 255, 255, 0.6) 100%, transparent),
      radial-gradient(1px 1px at 22% 65%, rgba(255, 255, 255, 0.6) 100%, transparent),
      radial-gradient(1px 1px at 35% 12%, rgba(255, 255, 255, 0.6) 100%, transparent),
      radial-gradient(1px 1px at 85% 32%, rgba(255, 255, 255, 0.6) 100%, transparent),
      radial-gradient(1px 1px at 10% 80%, rgba(255, 255, 255, 0.6) 100%, transparent),
      radial-gradient(1px 1px at 65% 92%, rgba(255, 255, 255, 0.6) 100%, transparent);
    background-size: 550px 550px;
    animation: stars-move 100s linear infinite;
  }

  @keyframes stars-move {
    0% {
      background-position: 0 0;
    }
    100% {
      background-position: 550px 550px;
    }
  }
}
