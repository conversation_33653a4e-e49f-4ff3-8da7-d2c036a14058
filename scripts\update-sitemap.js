#!/usr/bin/env node

/**
 * Script för att manuellt trigga sitemap-uppdatering
 * Kan köras när nya artiklar läggs till
 */

const fs = require('fs')
const path = require('path')

// Funktion för att kontrollera om en page.tsx fil innehåller redirect
function isRedirectPage(filePath) {
  try {
    if (!fs.existsSync(filePath)) {
      return false
    }

    const content = fs.readFileSync(filePath, 'utf8')
    // Kontrollera om filen innehåller redirect() funktionen och import från next/navigation
    const hasRedirect = content.includes('redirect(')
    const hasImport = content.includes('from "next/navigation"') || content.includes("from 'next/navigation'")
    return hasRedirect && hasImport
  } catch (error) {
    console.error(`Error reading file ${filePath}:`, error)
    return false
  }
}

// Funktion för att hitta alla page.tsx filer (exkluderar redirect-sidor)
function findAllPages(dir, basePath = '') {
  const pages = []

  try {
    const items = fs.readdirSync(dir, { withFileTypes: true })

    for (const item of items) {
      const fullPath = path.join(dir, item.name)
      const urlPath = path.join(basePath, item.name).replace(/\\/g, '/')

      if (item.isDirectory()) {
        // Kolla om det finns en page.tsx i denna mapp
        const pagePath = path.join(fullPath, 'page.tsx')
        if (fs.existsSync(pagePath)) {
          // Kontrollera om det är en redirect-sida
          if (!isRedirectPage(pagePath)) {
            pages.push(urlPath === '' ? '/' : `/${urlPath}`)
          } else {
            console.log(`🔄 Exkluderar redirect-sida: ${urlPath === '' ? '/' : `/${urlPath}`}`)
          }
        }

        // Rekursivt sök i undermappar
        pages.push(...findAllPages(fullPath, urlPath))
      }
    }
  } catch (error) {
    console.error(`Error reading directory ${dir}:`, error)
  }

  return pages
}

// Funktion för att validera sidor
function shouldIncludeInSitemap(url) {
  if (url.includes('[') || url.includes(']')) return false
  if (url.includes('/api/')) return false

  // Kontrollera om det är en redirect-sida
  const appDir = path.join(process.cwd(), 'app')
  const pagePath = path.join(appDir, url === '/' ? 'page.tsx' : `${url}/page.tsx`)
  if (isRedirectPage(pagePath)) {
    return false
  }

  const excludedPaths = ['/sitemap', '/404', '/500', '/error']
  return !excludedPaths.includes(url)
}

// Huvudfunktion
function updateSitemap() {
  console.log('🔄 Uppdaterar sitemap...')
  
  try {
    // Hitta alla sidor
    const appDir = path.join(process.cwd(), 'app')
    const allPages = findAllPages(appDir)
    const filteredPages = allPages.filter(shouldIncludeInSitemap)
    
    // Lägg till dynamiska sidor
    const zodiacSigns = [
      'vaduren', 'oxen', 'tvillingarna', 'kraftan', 'lejonet', 'jungfrun',
      'vagen', 'skorpionen', 'skytten', 'stenbocken', 'vattumannen', 'fiskarna'
    ]
    
    zodiacSigns.forEach(sign => {
      filteredPages.push(`/stjarntecken/${sign}`)
      filteredPages.push(`/horoskop/dagens/${sign}`)
      filteredPages.push(`/horoskop/veckans/${sign}`)
      filteredPages.push(`/horoskop/manadens/${sign}`)
      filteredPages.push(`/relationer/${sign}`)
    })
    
    // Lägg till kompatibilitetssidor
    zodiacSigns.forEach(sign1 => {
      zodiacSigns.forEach(sign2 => {
        filteredPages.push(`/relationer/${sign1}/${sign2}`)
      })
    })
    
    // Ta bort dubbletter
    const uniquePages = [...new Set(filteredPages)]
    
    console.log(`✅ Hittade ${uniquePages.length} sidor`)
    console.log('📄 Exempel på sidor som inkluderas:')
    uniquePages.slice(0, 10).forEach(page => console.log(`   ${page}`))
    
    if (uniquePages.length > 10) {
      console.log(`   ... och ${uniquePages.length - 10} till`)
    }
    
    // Logga uppdatering
    const timestamp = new Date().toISOString()
    const logMessage = `[${timestamp}] Sitemap uppdaterad med ${uniquePages.length} sidor`
    
    // Skapa logs-mapp om den inte finns
    const logsDir = path.join(process.cwd(), 'logs')
    if (!fs.existsSync(logsDir)) {
      fs.mkdirSync(logsDir, { recursive: true })
    }
    
    // Skriv till loggfil
    const logFile = path.join(logsDir, 'sitemap.log')
    fs.appendFileSync(logFile, logMessage + '\n')
    
    console.log('✅ Sitemap-uppdatering klar!')
    console.log('💡 Sitemap genereras automatiskt vid nästa build eller när /sitemap.xml besöks')
    
  } catch (error) {
    console.error('❌ Fel vid sitemap-uppdatering:', error)
    process.exit(1)
  }
}

// Kör script om det anropas direkt
if (require.main === module) {
  updateSitemap()
}

module.exports = { updateSitemap }
