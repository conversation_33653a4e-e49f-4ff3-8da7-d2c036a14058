"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { ChevronRight, Home } from "lucide-react"

// Create the component function
function Breadcrumbs() {
  const pathname = usePathname()

  if (pathname === "/") return null

  const pathSegments = pathname.split("/").filter(Boolean)

  // Map of path segments to display names
  const pathNames: Record<string, string> = {
    horoskop: "Horoskop",
    dagens: "Dagens",
    veckans: "Veckans",
    manadens: "Månadens",
    arshoroskop: "Årshoroskop",

    stjarntecken: "Stjärntecken",
    vaduren: "Väduren",
    oxen: "Oxen",
    tvillingarna: "Tvillingarna",
    kraftan: "Kräftan",
    lejonet: "Lejonet",
    jungfrun: "<PERSON>f<PERSON>",
    vagen: "Vågen",
    skorpionen: "Skorpionen",
    skytten: "Skytten",
    stenbocken: "Stenbocken",
    vattumannen: "Vattumannen",
    fiskarna: "<PERSON><PERSON><PERSON>",
    "astrologi-lara": "Astrologi Lära",
    grunderna: "<PERSON>run<PERSON><PERSON>",
    planeter: "Planeter",
    husen: "Husen",
    aspekter: "Aspekter",
    relationer: "Relationer",
    matchning: "Matchning",
    aktuellt: "Aktuellt",
    "om-oss": "Om Oss",
    kontakt: "Kontakt",
  }

  return (
    <div className="container flex py-4 mt-16 text-sm text-slate-300">
      <ol className="flex items-center flex-wrap">
        <li className="flex items-center">
          <Link href="/" className="flex items-center hover:text-white transition-colors duration-300">
            <Home className="h-3 w-3 mr-1 text-[#a78bfa]" />
            Hem
          </Link>
          {pathSegments.length > 0 && <ChevronRight className="h-3 w-3 mx-1 text-[#6e56cf]" />}
        </li>

        {pathSegments.map((segment, index) => {
          const path = `/${pathSegments.slice(0, index + 1).join("/")}`
          const isLast = index === pathSegments.length - 1

          return (
            <li key={path} className="flex items-center">
              {isLast ? (
                <span className="font-medium text-white">{pathNames[segment] || segment}</span>
              ) : (
                <>
                  <Link href={path} className="hover:text-[#a78bfa] transition-colors duration-300">
                    {pathNames[segment] || segment}
                  </Link>
                  <ChevronRight className="h-3 w-3 mx-1 text-[#6e56cf]" />
                </>
              )}
            </li>
          )
        })}
      </ol>
    </div>
  )
}

// Export as both default and named export
export { Breadcrumbs }
export default Breadcrumbs
