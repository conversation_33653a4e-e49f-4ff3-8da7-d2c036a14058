// Skript för att generera enkla stjärnteckensbilder
// Kör detta med Node.js för att skapa alla bilder på en gång

const fs = require('fs');
const path = require('path');
const { createCanvas } = require('canvas');

// Lista över alla stjärntecken
const zodiacSigns = [
  { name: "Väduren", path: "vaduren" },
  { name: "Oxen", path: "oxen" },
  { name: "T<PERSON>lingar<PERSON>", path: "tvillingarna" },
  { name: "<PERSON><PERSON><PERSON><PERSON><PERSON>", path: "kraftan" },
  { name: "<PERSON><PERSON><PERSON>", path: "lejonet" },
  { name: "<PERSON><PERSON><PERSON>", path: "jungfrun" },
  { name: "<PERSON>å<PERSON>", path: "vagen" },
  { name: "Skorpionen", path: "skorpionen" },
  { name: "Skytten", path: "skytten" },
  { name: "Stenbocken", path: "stenbocken" },
  { name: "Vattumannen", path: "vattumannen" },
  { name: "Fiskarna", path: "fiskarna" }
];

// Skapa en canvas för varje stjärntecken
zodiacSigns.forEach((sign, index) => {
  const canvas = createCanvas(200, 200);
  const ctx = canvas.getContext('2d');
  
  // Rensa canvas
  ctx.fillStyle = 'rgba(0, 0, 0, 0)';
  ctx.fillRect(0, 0, 200, 200);
  
  // Rita en cirkel
  ctx.beginPath();
  ctx.arc(100, 100, 80, 0, Math.PI * 2);
  ctx.strokeStyle = '#a78bfa';
  ctx.lineWidth = 2;
  ctx.stroke();
  
  // Beräkna färg baserat på index (för att få variation)
  const hue = (index * 30) % 360;
  ctx.strokeStyle = `hsl(${hue}, 70%, 60%)`;
  ctx.fillStyle = `hsl(${hue}, 70%, 60%)`;
  
  // Rita stjärnteckensymbol (enkel representation)
  ctx.beginPath();
  
  // Olika mönster för olika tecken
  switch(index) {
    case 0: // Väduren
      ctx.moveTo(70, 70);
      ctx.bezierCurveTo(100, 60, 130, 70, 130, 100);
      ctx.bezierCurveTo(130, 130, 100, 140, 70, 130);
      break;
    case 1: // Oxen
      ctx.moveTo(60, 80);
      ctx.bezierCurveTo(80, 60, 120, 60, 140, 80);
      ctx.moveTo(100, 60);
      ctx.lineTo(100, 140);
      break;
    case 2: // Tvillingarna
      ctx.moveTo(70, 60);
      ctx.lineTo(70, 140);
      ctx.moveTo(130, 60);
      ctx.lineTo(130, 140);
      ctx.moveTo(70, 80);
      ctx.lineTo(130, 80);
      ctx.moveTo(70, 120);
      ctx.lineTo(130, 120);
      break;
    case 3: // Kräftan
      ctx.moveTo(60, 100);
      ctx.bezierCurveTo(80, 70, 120, 70, 140, 100);
      ctx.moveTo(60, 100);
      ctx.bezierCurveTo(80, 130, 120, 130, 140, 100);
      break;
    case 4: // Lejonet
      ctx.arc(100, 100, 40, 0, Math.PI * 2);
      ctx.moveTo(140, 100);
      ctx.lineTo(160, 100);
      break;
    case 5: // Jungfrun
      ctx.moveTo(70, 70);
      ctx.lineTo(130, 70);
      ctx.moveTo(100, 70);
      ctx.lineTo(100, 130);
      ctx.lineTo(130, 130);
      break;
    case 6: // Vågen
      ctx.moveTo(60, 100);
      ctx.lineTo(140, 100);
      ctx.moveTo(70, 120);
      ctx.lineTo(130, 120);
      ctx.moveTo(100, 80);
      ctx.lineTo(100, 120);
      break;
    case 7: // Skorpionen
      ctx.moveTo(60, 80);
      ctx.lineTo(100, 80);
      ctx.lineTo(100, 120);
      ctx.lineTo(140, 120);
      ctx.lineTo(140, 140);
      break;
    case 8: // Skytten
      ctx.moveTo(60, 80);
      ctx.lineTo(100, 80);
      ctx.lineTo(100, 120);
      ctx.moveTo(100, 80);
      ctx.lineTo(140, 60);
      break;
    case 9: // Stenbocken
      ctx.moveTo(60, 80);
      ctx.bezierCurveTo(80, 60, 120, 60, 140, 80);
      ctx.bezierCurveTo(120, 100, 120, 120, 140, 140);
      break;
    case 10: // Vattumannen
      ctx.moveTo(60, 80);
      ctx.bezierCurveTo(80, 100, 120, 60, 140, 80);
      ctx.moveTo(60, 120);
      ctx.bezierCurveTo(80, 140, 120, 100, 140, 120);
      break;
    case 11: // Fiskarna
      ctx.moveTo(60, 80);
      ctx.bezierCurveTo(80, 60, 120, 60, 140, 80);
      ctx.moveTo(60, 120);
      ctx.bezierCurveTo(80, 140, 120, 140, 140, 120);
      ctx.moveTo(100, 60);
      ctx.lineTo(100, 140);
      break;
  }
  
  ctx.lineWidth = 4;
  ctx.stroke();
  
  // Spara bilden
  const buffer = canvas.toBuffer('image/png');
  fs.writeFileSync(path.join(__dirname, `${sign.path}.png`), buffer);
  
  console.log(`Skapade bild för ${sign.name}`);
});

console.log('Alla stjärnteckensbilder har skapats!');
