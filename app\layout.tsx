import type React from "react"
import type { <PERSON>ada<PERSON> } from "next"
import { <PERSON>, Playfair_Display, <PERSON>in<PERSON> } from "next/font/google"
import "./globals.css"
import Header from "@/components/layout/header"
import Footer from "@/components/layout/footer"

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
})

const playfair = Playfair_Display({
  subsets: ["latin"],
  variable: "--font-playfair",
})

const cinzel = Cinzel({
  subsets: ["latin"],
  variable: "--font-cinzel",
  weight: ["400", "500", "600", "700", "800"],
})

export const metadata: Metadata = {
  title: "Horoskopet.nu - Sveriges ledande astrologiportal",
  description:
    "Utforska dagliga horoskop, stjärntecken, astrologisk kunskap, relationskompatibilitet och aktuella astrologiska händelser.",
  generator: 'v0.dev',
  icons: {
    icon: '/favicon.png',
    apple: '/favicon.png',
    shortcut: '/favicon.png'
  }
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="sv">
      <body className={`${inter.variable} ${playfair.variable} ${cinzel.variable}`}>
        <div className="flex min-h-screen flex-col">
          <Header />
          <main className="flex-1">{children}</main>
          <Footer />
        </div>
      </body>
    </html>
  )
}
