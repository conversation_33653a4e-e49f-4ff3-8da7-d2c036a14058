import Link from "next/link"
import Image from "next/image"
import Breadcrumbs from "@/components/layout/breadcrumbs"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import type { Metadata } from "next"

export const metadata: Metadata = {
  title: "Grunderna i astrologi | Horoskopet.nu",
  description: "Lär dig astrologins grundläggande principer. Upptäck element, kvaliteter, zodiakens symbolik och hur födelsehoroskop skapas. Perfekt för nybörjare.",
  canonical: "https://horoskopet.nu/astrologi-lara/grunderna"
}

export default function AstrologiGrunderna() {
  const topics = [
    {
      title: "Element och Kvaliteter",
      description:
        "Lär dig om de fyra elementen (eld, jord, luft, vatten) och de tre kvaliteterna (kardinal, fast, rörlig).",
      image: "/images/element-och-kvaliteter-card.png",
      link: "/astrologi-lara/grunderna/element-och-kvaliteter",
    },
    {
      title: "Zodiakens Symbolik",
      description: "Utforska de djupa symbolerna och arketyperna bakom varje stjärntecken.",
      image: "/images/zodiakens-symbolik-card.png",
      link: "/astrologi-lara/grunderna/zodiakens-symbolik",
    },
    {
      title: "Födelsehoroskop",
      description: "Förstå vad ett födelsehoroskop är och hur det skapas baserat på din födelseinformation.",
      image: "/images/fodelsehoroskop-hero.png",
      link: "/astrologi-lara/grunderna/fodelsehoroskop",
    },
  ]

  return (
    <main className="container mx-auto px-4 py-28">
      <div className="mb-12 text-center">
        <h1 className="font-display text-4xl mb-4 text-white cosmic-title">Grunderna i Astrologi</h1>
        <p className="text-lg max-w-3xl mx-auto text-slate-300">
          Utforska astrologins grundläggande principer och lär dig hur denna uråldriga vetenskap kan hjälpa dig att
          förstå dig själv och världen omkring dig.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
        <div className="flex flex-col justify-center">
          <h2 className="font-display text-2xl mb-4 text-white">Vad är Astrologi?</h2>
          <p className="mb-4 text-slate-300">
            Astrologi är studiet av himlakropparnas rörelser och relativa positioner och deras påverkan på människors
            liv och naturliga världen. Det är ett symboliskt språk som hjälper oss att förstå de kosmiska energierna som
            påverkar oss.
          </p>
          <p className="text-slate-300">
            Astrologin har sina rötter i antika civilisationer och har utvecklats under tusentals år. Idag används den
            som ett verktyg för självkännedom, personlig utveckling och för att förstå livets cykler och mönster.
          </p>
        </div>
        <div className="relative h-64 md:h-auto rounded-lg overflow-hidden border border-[#6e56cf]/20 shadow-lg shadow-[#6e56cf]/5">
          <Image
            src="/images/astrologi-grunderna-hero.png"
            alt="Antik astrologisk karta"
            fill
            className="object-cover rounded-lg"
          />
        </div>
      </div>

      <h2 className="font-display text-2xl mb-6 text-white cosmic-title text-center">Utforska Grundläggande Ämnen</h2>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
        {topics.map((topic, index) => (
          <div key={index} className="bg-gradient-to-br from-[#1a1333]/80 to-[#2d1d57]/80 backdrop-blur-sm rounded-lg overflow-hidden border border-[#6e56cf]/20 hover:border-[#6e56cf]/40 transition-all duration-300 shadow-lg shadow-[#6e56cf]/5 hover:-translate-y-1">
            <div className="p-0">
              <div className="relative h-48 w-full">
                <Image
                  src={topic.image || "/placeholder.svg"}
                  alt={topic.title}
                  fill
                  className="object-cover rounded-t-lg"
                />
              </div>
            </div>
            <div className="p-6">
              <h3 className="text-xl mb-2 text-white font-display">{topic.title}</h3>
              <p className="text-slate-300 mb-4">{topic.description}</p>
            </div>
            <div className="px-6 pb-6">
              <Link
                href={topic.link}
                className="w-full inline-flex items-center justify-center rounded-md bg-gradient-to-r from-[#6e56cf]/20 to-[#a78bfa]/20 border border-[#6e56cf]/30 px-6 py-2 text-white hover:bg-gradient-to-r hover:from-[#6e56cf]/30 hover:to-[#a78bfa]/30 focus:outline-none focus:ring-2 focus:ring-[#a78bfa] focus:ring-offset-2 transition-all duration-300"
              >
                Läs mer
              </Link>
            </div>
          </div>
        ))}
      </div>

      <div className="bg-gradient-to-br from-[#1a1333]/80 to-[#2d1d57]/80 backdrop-blur-sm rounded-lg p-8 border border-[#6e56cf]/20 shadow-lg shadow-[#6e56cf]/5">
        <h2 className="font-display text-2xl mb-4 text-white">Börja Din Astrologiska Resa</h2>
        <p className="mb-4 text-slate-300">
          Oavsett om du är nybörjare eller har studerat astrologi tidigare, finns det alltid mer att lära. Utforska våra
          grundläggande guider för att bygga en solid grund för din astrologiska kunskap.
        </p>
        <p className="mb-6 text-slate-300">
          Kom ihåg att astrologi är ett verktyg för självreflektion och personlig insikt - inte en deterministisk
          vetenskap som förutspår framtiden med absolut säkerhet.
        </p>
        <Link
          href="/astrologi-lara/grunderna/element-och-kvaliteter"
          className="inline-flex items-center justify-center rounded-md bg-gradient-to-r from-[#6e56cf] to-[#a78bfa] px-6 py-2 text-white hover:from-[#a78bfa] hover:to-[#6e56cf] focus:outline-none focus:ring-2 focus:ring-[#a78bfa] focus:ring-offset-2 transition-all duration-300 shadow-md shadow-[#6e56cf]/20"
        >
          Börja med Element och Kvaliteter
        </Link>
      </div>
    </main>
  )
}
