import Link from "next/link"
import { Star, Mail, Instagram, Facebook, Twitter, MoonStar, Sun, Sparkles, Heart, Info } from "lucide-react"

export default function Footer() {
  return (
    <footer className="border-t border-[#6e56cf]/20 bg-gradient-to-b from-[#0c0817] to-[#1a1333] backdrop-blur-sm">
      <div className="container px-4 py-8 md:py-12">
        {/* Stjärnbakgrund overlay */}
        <div className="absolute inset-0 -z-10 overflow-hidden pointer-events-none">
          <div className="stars-small opacity-10"></div>
        </div>
        
        {/* Dekorativt element */}
        <div className="relative mb-8 flex justify-center">
          <div className="absolute top-0 h-px w-full max-w-md bg-gradient-to-r from-transparent via-[#6e56cf]/50 to-transparent"></div>
          <Star className="relative -top-3 h-6 w-6 text-[#a78bfa] animate-pulse-subtle" />
        </div>

        <div className="grid gap-8 sm:grid-cols-2 md:grid-cols-4 lg:grid-cols-5">
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <MoonStar className="h-6 w-6 text-[#a78bfa]" />
              <span className="font-display text-xl tracking-wide text-white cosmic-title">Horoskopet.nu</span>
            </div>
            <p className="text-sm text-slate-300 leading-relaxed">
              Sveriges ledande astrologiportal med dagliga horoskop, stjärntecken och astrologisk kunskap.
            </p>
            <div className="flex space-x-4 pt-2">
              <Link href="#" className="text-slate-300 hover:text-[#a78bfa] transition-colors duration-300">
                <Facebook className="h-5 w-5" />
                <span className="sr-only">Facebook</span>
              </Link>
              <Link href="#" className="text-slate-300 hover:text-[#a78bfa] transition-colors duration-300">
                <Instagram className="h-5 w-5" />
                <span className="sr-only">Instagram</span>
              </Link>
              <Link href="#" className="text-slate-300 hover:text-[#a78bfa] transition-colors duration-300">
                <Twitter className="h-5 w-5" />
                <span className="sr-only">Twitter</span>
              </Link>
            </div>
          </div>

          <div>
            <div className="flex items-center space-x-2 mb-4">
              <Sun className="h-5 w-5 text-[#a78bfa]" />
              <h3 className="font-display font-medium text-white">Horoskop</h3>
            </div>
            <ul className="space-y-3 text-sm">
              <li>
                <Link
                  href="/horoskop/dagens"
                  className="text-slate-300 hover:text-[#a78bfa] transition-colors duration-300 flex items-center group"
                >
                  <span className="mr-2 h-1.5 w-1.5 rounded-full bg-[#6e56cf]/50 group-hover:bg-[#a78bfa] transition-colors"></span>
                  Dagens Horoskop
                </Link>
              </li>
              <li>
                <Link
                  href="/horoskop/veckans"
                  className="text-slate-300 hover:text-[#a78bfa] transition-colors duration-300 flex items-center group"
                >
                  <span className="mr-2 h-1.5 w-1.5 rounded-full bg-[#6e56cf]/50 group-hover:bg-[#a78bfa] transition-colors"></span>
                  Veckans Horoskop
                </Link>
              </li>
              <li>
                <Link
                  href="/horoskop/manadens"
                  className="text-slate-300 hover:text-[#a78bfa] transition-colors duration-300 flex items-center group"
                >
                  <span className="mr-2 h-1.5 w-1.5 rounded-full bg-[#6e56cf]/50 group-hover:bg-[#a78bfa] transition-colors"></span>
                  Månadens Horoskop
                </Link>
              </li>
            </ul>
          </div>

          <div>
            <div className="flex items-center space-x-2 mb-4">
              <Sparkles className="h-5 w-5 text-[#a78bfa]" />
              <h3 className="font-display font-medium text-white">Stjärntecken</h3>
            </div>
            <div className="grid grid-cols-2 gap-x-4 gap-y-3 text-sm">
              <Link
                href="/stjarntecken/vaduren"
                className="text-slate-300 hover:text-[#a78bfa] transition-colors duration-300 flex items-center group"
              >
                <span className="mr-2 h-1.5 w-1.5 rounded-full bg-[#6e56cf]/50 group-hover:bg-[#a78bfa] transition-colors"></span>
                Väduren
              </Link>
              <Link
                href="/stjarntecken/oxen"
                className="text-slate-300 hover:text-[#a78bfa] transition-colors duration-300 flex items-center group"
              >
                <span className="mr-2 h-1.5 w-1.5 rounded-full bg-[#6e56cf]/50 group-hover:bg-[#a78bfa] transition-colors"></span>
                Oxen
              </Link>
              <Link
                href="/stjarntecken/tvillingarna"
                className="text-slate-300 hover:text-[#a78bfa] transition-colors duration-300 flex items-center group"
              >
                <span className="mr-2 h-1.5 w-1.5 rounded-full bg-[#6e56cf]/50 group-hover:bg-[#a78bfa] transition-colors"></span>
                Tvillingarna
              </Link>
              <Link
                href="/stjarntecken/kraftan"
                className="text-slate-300 hover:text-[#a78bfa] transition-colors duration-300 flex items-center group"
              >
                <span className="mr-2 h-1.5 w-1.5 rounded-full bg-[#6e56cf]/50 group-hover:bg-[#a78bfa] transition-colors"></span>
                Kräftan
              </Link>
              <Link
                href="/stjarntecken/lejonet"
                className="text-slate-300 hover:text-[#a78bfa] transition-colors duration-300 flex items-center group"
              >
                <span className="mr-2 h-1.5 w-1.5 rounded-full bg-[#6e56cf]/50 group-hover:bg-[#a78bfa] transition-colors"></span>
                Lejonet
              </Link>
              <Link
                href="/stjarntecken/jungfrun"
                className="text-slate-300 hover:text-[#a78bfa] transition-colors duration-300 flex items-center group"
              >
                <span className="mr-2 h-1.5 w-1.5 rounded-full bg-[#6e56cf]/50 group-hover:bg-[#a78bfa] transition-colors"></span>
                Jungfrun
              </Link>
              <Link
                href="/stjarntecken/vagen"
                className="text-slate-300 hover:text-[#a78bfa] transition-colors duration-300 flex items-center group"
              >
                <span className="mr-2 h-1.5 w-1.5 rounded-full bg-[#6e56cf]/50 group-hover:bg-[#a78bfa] transition-colors"></span>
                Vågen
              </Link>
              <Link
                href="/stjarntecken/skorpionen"
                className="text-slate-300 hover:text-[#a78bfa] transition-colors duration-300 flex items-center group"
              >
                <span className="mr-2 h-1.5 w-1.5 rounded-full bg-[#6e56cf]/50 group-hover:bg-[#a78bfa] transition-colors"></span>
                Skorpionen
              </Link>
              <Link
                href="/stjarntecken/skytten"
                className="text-slate-300 hover:text-[#a78bfa] transition-colors duration-300 flex items-center group"
              >
                <span className="mr-2 h-1.5 w-1.5 rounded-full bg-[#6e56cf]/50 group-hover:bg-[#a78bfa] transition-colors"></span>
                Skytten
              </Link>
              <Link
                href="/stjarntecken/stenbocken"
                className="text-slate-300 hover:text-[#a78bfa] transition-colors duration-300 flex items-center group"
              >
                <span className="mr-2 h-1.5 w-1.5 rounded-full bg-[#6e56cf]/50 group-hover:bg-[#a78bfa] transition-colors"></span>
                Stenbocken
              </Link>
              <Link
                href="/stjarntecken/vattumannen"
                className="text-slate-300 hover:text-[#a78bfa] transition-colors duration-300 flex items-center group"
              >
                <span className="mr-2 h-1.5 w-1.5 rounded-full bg-[#6e56cf]/50 group-hover:bg-[#a78bfa] transition-colors"></span>
                Vattumannen
              </Link>
              <Link
                href="/stjarntecken/fiskarna"
                className="text-slate-300 hover:text-[#a78bfa] transition-colors duration-300 flex items-center group"
              >
                <span className="mr-2 h-1.5 w-1.5 rounded-full bg-[#6e56cf]/50 group-hover:bg-[#a78bfa] transition-colors"></span>
                Fiskarna
              </Link>
            </div>
          </div>

          <div>
            <div className="flex items-center space-x-2 mb-4">
              <Star className="h-5 w-5 text-[#a78bfa]" />
              <h3 className="font-display font-medium text-white">Kunskap</h3>
            </div>
            <ul className="space-y-3 text-sm">
              <li>
                <Link
                  href="/astrologi-lara/grunderna"
                  className="text-slate-300 hover:text-[#a78bfa] transition-colors duration-300 flex items-center group"
                >
                  <span className="mr-2 h-1.5 w-1.5 rounded-full bg-[#6e56cf]/50 group-hover:bg-[#a78bfa] transition-colors"></span>
                  Astrologins Grunder
                </Link>
              </li>
              <li>
                <Link
                  href="/astrologi-lara/planeter"
                  className="text-slate-300 hover:text-[#a78bfa] transition-colors duration-300 flex items-center group"
                >
                  <span className="mr-2 h-1.5 w-1.5 rounded-full bg-[#6e56cf]/50 group-hover:bg-[#a78bfa] transition-colors"></span>
                  Planeter
                </Link>
              </li>
              <li>
                <Link
                  href="/astrologi-lara/husen"
                  className="text-slate-300 hover:text-[#a78bfa] transition-colors duration-300 flex items-center group"
                >
                  <span className="mr-2 h-1.5 w-1.5 rounded-full bg-[#6e56cf]/50 group-hover:bg-[#a78bfa] transition-colors"></span>
                  Husen
                </Link>
              </li>
              <li>
                <Link
                  href="/astrologi-lara/aspekter"
                  className="text-slate-300 hover:text-[#a78bfa] transition-colors duration-300 flex items-center group"
                >
                  <span className="mr-2 h-1.5 w-1.5 rounded-full bg-[#6e56cf]/50 group-hover:bg-[#a78bfa] transition-colors"></span>
                  Aspekter
                </Link>
              </li>
              <li>
                <Link
                  href="/relationer"
                  className="text-slate-300 hover:text-[#a78bfa] transition-colors duration-300 flex items-center group"
                >
                  <span className="mr-2 h-1.5 w-1.5 rounded-full bg-[#6e56cf]/50 group-hover:bg-[#a78bfa] transition-colors"></span>
                  Relationer & Kompatibilitet
                </Link>
              </li>
              <li>
                <Link
                  href="/aktuellt"
                  className="text-slate-300 hover:text-[#a78bfa] transition-colors duration-300 flex items-center group"
                >
                  <span className="mr-2 h-1.5 w-1.5 rounded-full bg-[#6e56cf]/50 group-hover:bg-[#a78bfa] transition-colors"></span>
                  Aktuella Händelser
                </Link>
              </li>
            </ul>
          </div>

          <div>
            <div className="flex items-center space-x-2 mb-4">
              <Info className="h-5 w-5 text-[#a78bfa]" />
              <h3 className="font-display font-medium text-white">Information</h3>
            </div>
            <ul className="space-y-3 text-sm">
              <li>
                <Link
                  href="/om-oss"
                  className="text-slate-300 hover:text-[#a78bfa] transition-colors duration-300 flex items-center group"
                >
                  <span className="mr-2 h-1.5 w-1.5 rounded-full bg-[#6e56cf]/50 group-hover:bg-[#a78bfa] transition-colors"></span>
                  Om Oss
                </Link>
              </li>
              <li>
                <Link
                  href="/kontakt"
                  className="text-slate-300 hover:text-[#a78bfa] transition-colors duration-300 flex items-center group"
                >
                  <span className="mr-2 h-1.5 w-1.5 rounded-full bg-[#6e56cf]/50 group-hover:bg-[#a78bfa] transition-colors"></span>
                  Kontakt
                </Link>
              </li>
              <li>
                <Link
                  href="/integritetspolicy"
                  className="text-slate-300 hover:text-[#a78bfa] transition-colors duration-300 flex items-center group"
                >
                  <span className="mr-2 h-1.5 w-1.5 rounded-full bg-[#6e56cf]/50 group-hover:bg-[#a78bfa] transition-colors"></span>
                  Integritetspolicy
                </Link>
              </li>
              <li>
                <Link
                  href="/cookies"
                  className="text-slate-300 hover:text-[#a78bfa] transition-colors duration-300 flex items-center group"
                >
                  <span className="mr-2 h-1.5 w-1.5 rounded-full bg-[#6e56cf]/50 group-hover:bg-[#a78bfa] transition-colors"></span>
                  Cookies
                </Link>
              </li>
              <li>
                <Link
                  href="/sitemap"
                  className="text-slate-300 hover:text-[#a78bfa] transition-colors duration-300 flex items-center group"
                >
                  <span className="mr-2 h-1.5 w-1.5 rounded-full bg-[#6e56cf]/50 group-hover:bg-[#a78bfa] transition-colors"></span>
                  Sitemap
                </Link>
              </li>
            </ul>
          </div>
        </div>

        {/* Dekorativ linje */}
        <div className="relative my-8">
          <div className="absolute inset-0 flex items-center">
            <div className="w-full border-t border-[#6e56cf]/20"></div>
          </div>
          <div className="relative flex justify-center">
            <span className="bg-[#0c0817] px-4 text-xs text-slate-300/80">Stjärnorna guidar oss</span>
          </div>
        </div>

        <div className="flex flex-col sm:flex-row justify-between items-center gap-4">
          <p className="text-sm text-slate-300">
            © {new Date().getFullYear()} Horoskopet.nu. Alla rättigheter förbehållna.
          </p>
          <div className="flex items-center gap-4">
            <Link
              href="/kontakt"
              className="text-sm text-slate-300 hover:text-[#a78bfa] transition-colors duration-300 flex items-center gap-1 group"
            >
              <Mail className="h-4 w-4 group-hover:scale-110 transition-transform duration-300" />
              Kontakta oss
            </Link>
            <Link
              href="/nyhetsbrev"
              className="text-sm text-slate-300 hover:text-[#a78bfa] transition-colors duration-300 flex items-center gap-1 group"
            >
              <Heart className="h-4 w-4 group-hover:scale-110 transition-transform duration-300" />
              Prenumerera
            </Link>
          </div>
        </div>
      </div>
    </footer>
  )
}
