import fs from 'fs'
import path from 'path'

/**
 * Utility-funktioner för sitemap-hantering
 */

// Interface för sidor som ska läggas till i sitemap
export interface SitemapPage {
  url: string
  priority?: number
  changeFrequency?: 'always' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'never'
  lastModified?: Date
}

// Funktion för att kontrollera om en page.tsx fil innehåller redirect
export function isRedirectPage(filePath: string): boolean {
  try {
    if (!fs.existsSync(filePath)) {
      return false
    }

    const content = fs.readFileSync(filePath, 'utf8')
    // Kontrollera om filen innehåller redirect() funktionen och import från next/navigation
    const hasRedirect = content.includes('redirect(')
    const hasImport = content.includes('from "next/navigation"') || content.includes("from 'next/navigation'")
    return hasRedirect && hasImport
  } catch (error) {
    console.error(`<PERSON>rror reading file ${filePath}:`, error)
    return false
  }
}

// Funktion för att hitta alla page.tsx filer rekursivt (exkluderar redirect-sidor)
export function findAllPages(dir: string, basePath: string = ''): string[] {
  const pages: string[] = []

  try {
    const items = fs.readdirSync(dir, { withFileTypes: true })

    for (const item of items) {
      const fullPath = path.join(dir, item.name)
      const urlPath = path.join(basePath, item.name).replace(/\\/g, '/')

      if (item.isDirectory()) {
        // Kolla om det finns en page.tsx i denna mapp
        const pagePath = path.join(fullPath, 'page.tsx')
        if (fs.existsSync(pagePath)) {
          // Kontrollera om det är en redirect-sida
          if (!isRedirectPage(pagePath)) {
            pages.push(urlPath === '' ? '/' : `/${urlPath}`)
          } else {
            console.log(`🔄 Exkluderar redirect-sida: ${urlPath === '' ? '/' : `/${urlPath}`}`)
          }
        }

        // Rekursivt sök i undermappar
        pages.push(...findAllPages(fullPath, urlPath))
      }
    }
  } catch (error) {
    console.error(`Error reading directory ${dir}:`, error)
  }

  return pages
}

// Funktion för att validera om en sida ska inkluderas i sitemap
export function shouldIncludeInSitemap(url: string): boolean {
  // Ta bort dynamiska routes
  if (url.includes('[') || url.includes(']')) {
    return false
  }

  // Ta bort API routes
  if (url.includes('/api/')) {
    return false
  }

  // Kontrollera om det är en redirect-sida
  const appDir = path.join(process.cwd(), 'app')
  const pagePath = path.join(appDir, url === '/' ? 'page.tsx' : `${url}/page.tsx`)
  if (isRedirectPage(pagePath)) {
    return false
  }

  // Ta bort specifika sidor
  const excludedPaths = [
    '/sitemap',
    '/404',
    '/500',
    '/error',
    '/admin',
    '/private'
  ]

  return !excludedPaths.some(excluded => url.startsWith(excluded))
}

// Funktion för att automatiskt bestämma prioritet
export function getAutoPriority(url: string): number {
  if (url === '/') return 1.0
  if (url.includes('/horoskop/dagens') || url.includes('/horoskop/veckans') || url.includes('/horoskop/manadens')) return 0.9
  if (url.includes('/stjarntecken/')) return 0.8
  if (url.includes('/horoskop')) return 0.8
  if (url.includes('/astrologi-lara')) return 0.7
  if (url.includes('/relationer')) return 0.7
  if (url.includes('/aktuellt')) return 0.6
  return 0.5
}

// Funktion för att automatiskt bestämma uppdateringsfrekvens
export function getAutoChangeFrequency(url: string): 'always' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'never' {
  if (url.includes('/horoskop/dagens')) return 'daily'
  if (url.includes('/horoskop/veckans')) return 'weekly'
  if (url.includes('/horoskop/manadens')) return 'monthly'
  if (url.includes('/aktuellt')) return 'weekly'
  if (url === '/') return 'daily'
  if (url.includes('/horoskop')) return 'weekly'
  return 'monthly'
}

// Funktion för att logga sitemap-ändringar
export function logSitemapUpdate(action: string, url?: string) {
  const timestamp = new Date().toISOString()
  const logMessage = url 
    ? `[${timestamp}] Sitemap ${action}: ${url}`
    : `[${timestamp}] Sitemap ${action}`
  
  console.log(logMessage)
  
  // Optionellt: spara till loggfil
  try {
    const logDir = path.join(process.cwd(), 'logs')
    if (!fs.existsSync(logDir)) {
      fs.mkdirSync(logDir, { recursive: true })
    }
    
    const logFile = path.join(logDir, 'sitemap.log')
    fs.appendFileSync(logFile, logMessage + '\n')
  } catch (error) {
    console.error('Failed to write to log file:', error)
  }
}

// Funktion för att få alla stjärntecken (för dynamiska routes)
export function getZodiacSigns(): string[] {
  return [
    'vaduren', 'oxen', 'tvillingarna', 'kraftan', 'lejonet', 'jungfrun',
    'vagen', 'skorpionen', 'skytten', 'stenbocken', 'vattumannen', 'fiskarna'
  ]
}

// Funktion för att generera alla kompatibilitetssidor
export function generateCompatibilityPages(): string[] {
  const signs = getZodiacSigns()
  const pages: string[] = []
  
  signs.forEach(sign1 => {
    signs.forEach(sign2 => {
      pages.push(`/relationer/${sign1}/${sign2}`)
    })
  })
  
  return pages
}

// Funktion för att generera alla horoskopsidor
export function generateHoroscopePages(): string[] {
  const signs = getZodiacSigns()
  const pages: string[] = []
  
  signs.forEach(sign => {
    pages.push(`/stjarntecken/${sign}`)
    pages.push(`/horoskop/dagens/${sign}`)
    pages.push(`/horoskop/veckans/${sign}`)
    pages.push(`/horoskop/manadens/${sign}`)
    pages.push(`/relationer/${sign}`)
  })
  
  return pages
}

// Huvudfunktion för att få alla sidor som ska inkluderas i sitemap
export function getAllSitemapPages(): SitemapPage[] {
  // Hitta alla statiska sidor
  const appDir = path.join(process.cwd(), 'app')
  const staticPages = findAllPages(appDir).filter(shouldIncludeInSitemap)
  
  // Lägg till dynamiska sidor
  const dynamicPages = [
    ...generateHoroscopePages(),
    ...generateCompatibilityPages()
  ]
  
  // Kombinera alla sidor
  const allPages = [...new Set([...staticPages, ...dynamicPages])]
  
  // Konvertera till SitemapPage-objekt
  return allPages.map(url => ({
    url,
    priority: getAutoPriority(url),
    changeFrequency: getAutoChangeFrequency(url),
    lastModified: new Date()
  }))
}
