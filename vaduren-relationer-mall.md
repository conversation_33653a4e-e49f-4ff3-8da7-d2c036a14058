# Väduren Relationer - Komplett Mall för Stjärnteckensidor

## 📋 Översikt
Denna mall baseras på den fullständigt implementerade och felfria Vädurens relationsida (`/app/relationer/vaduren/page.tsx`). Använd denna som exakt mall för alla andra stjärnteckensidor.

## 🎨 Cosmic Design System
Alla sektioner följer det kosmiska temat från `design-system.md`:

### Färgschema
- **Primär**: `#6e56cf` (cosmic purple)
- **Accent**: `#a78bfa` (light purple)
- **Bakgrund**: `#1a1333` (dark cosmic)
- **Text**: `slate-300` (ljus text)

### Komponenter
- **Cards**: `border border-[#6e56cf]/30 bg-[#1a1333]/80 shadow-lg shadow-primary/10`
- **Ikoner**: Star, Sparkles, Heart med cosmic färger
- **Typography**: `font-display` för rubriker
- **Hover-effekter**: `hover:border-[#6e56cf]/50 hover:bg-[#1a1333]/60`

## 🏗️ Sidstruktur

### 1. Imports & Helper-funktion
```typescript
import { Breadcrumbs } from "@/components/layout/breadcrumbs"
import { Card, CardContent } from "@/components/ui/card"
import { zodiacData } from "@/lib/zodiac-data"
import Image from "next/image"
import Link from "next/link"
import { Star, Sparkles, Heart } from "lucide-react"

// Helper function to get the correct image for each zodiac sign
function getZodiacImage(path: string): string {
  const imageMap: Record<string, string> = {
    vaduren: "/images/aries.jpg",
    oxen: "/images/taurus.jpg",
    tvillingarna: "/images/gemini.jpg",
    kraftan: "/images/cancer.jpg",
    lejonet: "/images/leo.jpg",
    jungfrun: "/images/virgo.jpg",
    vagen: "/images/libra.jpg",
    skorpionen: "/images/scorpio.jpg",
    skytten: "/images/sagittarius.jpg",
    stenbocken: "/images/capricorn.jpg",
    vattumannen: "/images/aquarius.jpg",
    fiskarna: "/images/pisces.jpg"
  }
  return imageMap[path] || "/images/aries.jpg"
}
```

### 2. Sidkomponent
```typescript
export default function [STJÄRNTECKEN]RelationerPage() {
  const zodiacSign = zodiacData.find(
    (sign) => sign.slug === "[slug]" || sign.path === "[path]" || sign.name.toLowerCase() === "[namn]",
  )

  if (!zodiacSign) {
    return <div>Stjärntecken hittades inte</div>
  }
```

### 3. Stjärnbakgrund (VIKTIGT - Använd Denna!)
```jsx
{/* Stjärnbakgrund overlay */}
<div className="fixed inset-0 -z-10 overflow-hidden pointer-events-none">
  <div className="absolute inset-0 bg-[#0c0817] opacity-90"></div>
  <div className="stars-small opacity-30"></div>
</div>
```
**OBS**: Använd DENNA bakgrund, inte den gamla cosmic-bg som skapar mörka overlays!

### 4. Hero-sektion (Optimerad spacing)
```jsx
<section className="relative min-h-[60vh] flex items-center justify-center overflow-hidden">
  <div className="relative z-10 container mx-auto px-4 text-center">
    <div className="max-w-4xl mx-auto">
      <div className="relative w-32 h-32 mx-auto mb-6">
        <Image
          src={getZodiacImage(zodiacSign.path)}
          alt={zodiacSign.name}
          fill
          className="object-contain"
        />
      </div>
      <h1 className="text-4xl md:text-5xl lg:text-6xl font-display tracking-tight mb-6 cosmic-title leading-tight">
        {zodiacSign.name} i Relationer
      </h1>
      <p className="text-lg md:text-xl text-slate-300 mb-8 max-w-2xl mx-auto drop-shadow-md">
        [BESKRIVNING AV STJÄRNTECKNET I RELATIONER]
      </p>
    </div>
  </div>
</section>

{/* Container med optimerad spacing */}
<div className="container mx-auto px-4 py-8">
  <div className="max-w-6xl mx-auto space-y-12">
    <Breadcrumbs />
```

**Förbättringar**:
- Hero-höjd: `min-h-[60vh]` (från 80vh) för bättre proportioner
- Container padding: `py-8` (från py-16) för mindre gap
- Sektionsavstånd: `space-y-12` (från space-y-16) för tätare layout

## 📝 Sektioner att Inkludera

### 1. Breadcrumbs
```jsx
<Breadcrumbs />
```
**OBS**: Breadcrumbs-komponenten använder automatisk path-baserad navigation och behöver inte `items` prop.

### 2. Stjärnteckenskort (3-kolumn grid)
- Stjärnteckensbild med `getZodiacImage(zodiacSign.path)`
- Grundläggande information
- Länk till stjärnteckenssida

### 3. Styrkor & Utmaningar (2-kolumn grid)
- **Styrkor i Relationer**: Lista med `key="strength-1"` etc.
- **Utmaningar i Relationer**: Lista med `key="challenge-1"` etc.

### 4. Kompatibilitet (3-kolumn grid)
- **Bästa Matchningar**: `key="best-match-1"` etc.
- **Utmanande Matchningar**: `key="challenging-match-1"` etc.
- **Neutrala Matchningar**: `key="neutral-match-1"` etc.

### 5. Tips för Relationer (2-kolumn grid)
- **För Partners**: `key="partner-tip-1"` etc.
- **För Stjärntecknet**: `key="[tecken]-tip-1"` etc.

### 6. Kärleksspråk (2-kolumn grid)
- **Hur de Uttrycker Kärlek**: `key="love-expression-1"` etc.
- **Hur de Vill Ta Emot Kärlek**: `key="love-receiving-1"` etc.

### 7. Relationsfaser (3-kolumn grid)
- **Dejting & Förälskelse**
- **Långvariga Förhållanden**
- **Konflikter & Försoning**

### 8. Navigation mellan Stjärntecken
```jsx
<div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
  {zodiacData.map((sign) => (
    <Link
      key={sign.path}
      href={`/relationer/${sign.path}`}
      className={`p-4 rounded-lg text-center transition-all duration-300 border border-[#6e56cf]/30 hover:border-[#6e56cf]/50 hover:bg-[#1a1333]/60 ${
        sign.path === zodiacSign.path ? "bg-[#1a1333]/80 border-[#a78bfa]/50" : "bg-[#1a1333]/40"
      }`}
    >
      <div className="w-12 h-12 relative mx-auto mb-2">
        <Image
          src={getZodiacImage(sign.path)}
          alt={sign.name}
          fill
          className="object-contain"
        />
      </div>
      <span className={`${sign.path === zodiacSign.path ? "font-bold text-[#a78bfa]" : "text-slate-300"}`}>
        {sign.name}
      </span>
    </Link>
  ))}
</div>
```

## ⚠️ Viktiga Tekniska Detaljer

### React Keys
**ALLA** li-element MÅSTE ha unika keys:
```jsx
<li key="unique-key-1" className="...">
<li key="unique-key-2" className="...">
```

### Bildhantering
Använd ALLTID `getZodiacImage(sign.path)` istället för `sign.image`

### Navigation Keys (KRITISKT!)
Använd ALLTID `sign.path` som key och för jämförelser, INTE `sign.slug`:
```jsx
key={sign.path}                    // ✅ KORREKT
href={`/relationer/${sign.path}`}  // ✅ KORREKT
sign.path === zodiacSign.path      // ✅ KORREKT

// ❌ ANVÄND INTE:
key={sign.slug}                    // ❌ FINNS INTE
href={`/relationer/${sign.slug}`}  // ❌ GÅR TILL undefined
```

**VIKTIGT**: `zodiacData` har ENDAST `path` property, INTE `slug`!

### JSX-struktur
Se till att alla div-element är korrekt nestade utan felaktig struktur

### Textfärger (VIKTIGT!)
Följ design-system.md exakt:
- **Huvudtext**: `text-white` för paragrafer och viktiga begrepp
- **Sekundär text**: `text-slate-300` för beskrivningar
- **Listor**: Ingen färg på `<ul>`, använd `<strong className="text-white">` + `<span className="text-slate-300">`

### Cosmic Styling
Alla Cards ska ha: `className="overflow-hidden border border-[#6e56cf]/30 bg-[#1a1333]/80 shadow-lg shadow-primary/10"`

## 🎯 Anpassningar för Andra Stjärntecken

### 1. Ändra Filnamn
`/app/relationer/[stjärntecken]/page.tsx`

### 2. Ändra Komponentnamn
`[Stjärntecken]RelationerPage`

### 3. Ändra zodiacSign.find()
```typescript
const zodiacSign = zodiacData.find(
  (sign) => sign.slug === "[slug]" || sign.path === "[path]" || sign.name.toLowerCase() === "[namn]",
)
```

### 4. Anpassa Innehåll
- Hero-beskrivning
- Styrkor och utmaningar
- Kompatibilitet med andra tecken
- Tips specifika för tecknet
- Kärleksspråk
- Relationsfaser

## ✅ Kvalitetskontroll

Innan du är klar, kontrollera:
- [ ] Inga syntaxfel
- [ ] Alla bilder fungerar
- [ ] Alla listor har unika keys
- [ ] Cosmic design implementerat
- [ ] Navigation fungerar
- [ ] Responsiv design

## 📁 Filstruktur
```
app/relationer/[stjärntecken]/
└── page.tsx (använd denna mall)
```

Denna mall är testad och felfri - använd den som exakt mall för alla 11 återstående stjärnteckensidor!
