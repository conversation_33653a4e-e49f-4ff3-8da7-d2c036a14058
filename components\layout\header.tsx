"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { Menu, ChevronDown, Star, BookOpen, Heart, Calendar, Info, Search, Moon, Sun, Sparkles } from "lucide-react"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet"

const mainNavItems = [
  {
    title: "Horosko<PERSON>",
    href: "/horoskop",
    icon: <Sparkles className="h-4 w-4" />,
    submenu: [
      { title: "Dagens Horoskop", href: "/horoskop/dagens" },
      { title: "Veckans Horoskop", href: "/horoskop/veckans" },
      { title: "<PERSON><PERSON><PERSON><PERSON><PERSON> Ho<PERSON>", href: "/horoskop/manadens" },
    ],
  },
  {
    title: "Stjärntecken",
    href: "/stjarntecken",
    icon: <Star className="h-4 w-4" />,
    submenu: [
      { title: "Väduren", href: "/stjarntecken/vaduren" },
      { title: "Oxen", href: "/stjarntecken/oxen" },
      { title: "Tvillingarna", href: "/stjarntecken/tvillingarna" },
      { title: "Kräftan", href: "/stjarntecken/kraftan" },
      { title: "Lejonet", href: "/stjarntecken/lejonet" },
      { title: "Jungfrun", href: "/stjarntecken/jungfrun" },
      { title: "Vågen", href: "/stjarntecken/vagen" },
      { title: "Skorpionen", href: "/stjarntecken/skorpionen" },
      { title: "Skytten", href: "/stjarntecken/skytten" },
      { title: "Stenbocken", href: "/stjarntecken/stenbocken" },
      { title: "Vattumannen", href: "/stjarntecken/vattumannen" },
      { title: "Fiskarna", href: "/stjarntecken/fiskarna" },
    ],
  },
  {
    title: "Astrologi Lära",
    href: "/astrologi-lara",
    icon: <BookOpen className="h-4 w-4" />,
    submenu: [
      { title: "Grunderna", href: "/astrologi-lara/grunderna" },
      { title: "Planeter", href: "/astrologi-lara/planeter" },
      { title: "Husen", href: "/astrologi-lara/husen" },
      { title: "Aspekter", href: "/astrologi-lara/aspekter" },
    ],
  },
  {
    title: "Relationer",
    href: "/relationer",
    icon: <Heart className="h-4 w-4" />,
    submenu: [
      { title: "Stjärntecken Matchning", href: "/relationer/matchning" },
    ],
  },
  {
    title: "Aktuellt",
    href: "/aktuellt",
    icon: <Calendar className="h-4 w-4" />,
  },
]

export default function Header() {
  const pathname = usePathname()
  const [isOpen, setIsOpen] = useState(false)
  const [scrolled, setScrolled] = useState(false)

  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 20)
    }
    window.addEventListener("scroll", handleScroll)
    return () => window.removeEventListener("scroll", handleScroll)
  }, [])

  return (
    <header
      className={cn(
        "fixed top-0 z-50 w-full transition-all duration-300",
        scrolled
          ? "bg-[#0c0817]/80 backdrop-blur-md border-b border-[#6e56cf]/20 shadow-md"
          : "bg-transparent",
      )}
    >
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="stars-small absolute inset-0 opacity-30"></div>
        {scrolled && <div className="absolute inset-0 bg-gradient-to-b from-[#1a1333]/80 to-[#2d1d57]/80 opacity-50"></div>}
      </div>

      <div className="container relative z-10">
        <div className="flex h-16 items-center justify-between">
          <div className="flex items-center gap-6 md:gap-8 lg:gap-10">
            <Link href="/" className="group flex items-center space-x-2 pl-1 md:pl-0">
              <div className="relative">
                <Star className="h-7 w-7 text-[#a78bfa] transition-all duration-500 group-hover:text-white group-hover:rotate-45" />
                <div className="absolute inset-0 bg-[#6e56cf]/30 rounded-full blur-md scale-0 group-hover:scale-150 transition-all duration-500"></div>
              </div>
              <span className="font-display text-xl hidden sm:inline-block cosmic-title">
                Horoskopet.nu
              </span>
            </Link>

            <nav className="hidden md:flex items-center gap-6">
              {mainNavItems.map((item) =>
                item.submenu ? (
                  <DropdownMenu key={item.href}>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="ghost"
                        className={cn(
                          "flex items-center gap-1 px-2 transition-all duration-300",
                          "hover:bg-[#6e56cf]/20 hover:text-white relative group",
                          pathname.startsWith(item.href)
                            ? "font-medium text-white after:absolute after:bottom-0 after:left-0 after:h-0.5 after:w-full after:bg-[#a78bfa]/70 after:rounded-full"
                            : "text-slate-300",
                        )}
                      >
                        <span className="flex items-center gap-1.5">
                          {item.icon && (
                            <span
                              className={cn(
                                "transition-transform duration-300 group-hover:rotate-12",
                                pathname.startsWith(item.href) ? "text-[#a78bfa]" : "text-slate-300",
                              )}
                            >
                              {item.icon}
                            </span>
                          )}
                          {item.title}
                        </span>
                        <ChevronDown className="h-4 w-4 transition-transform duration-300 group-hover:translate-y-0.5" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent
                      align="start"
                      className="w-52 bg-gradient-to-br from-[#1a1333]/90 to-[#2d1d57]/90 backdrop-blur-md border-[#6e56cf]/20 shadow-lg shadow-[#6e56cf]/10 rounded-lg overflow-hidden"
                    >
                      <div className="absolute inset-0 stars-small opacity-10 pointer-events-none"></div>
                      <div className="relative z-10">
                        <DropdownMenuItem asChild>
                          <Link
                            href={item.href}
                            className="w-full hover:bg-[#6e56cf]/20 hover:text-white font-medium text-white flex items-center gap-2 py-2"
                          >
                            {item.icon}
                            Alla {item.title.toLowerCase()}
                          </Link>
                        </DropdownMenuItem>
                        <div className="h-px bg-gradient-to-r from-transparent via-[#a78bfa]/20 to-transparent my-1"></div>
                        {item.submenu.map((subItem) => (
                          <DropdownMenuItem key={subItem.href} asChild>
                            <Link
                              href={subItem.href}
                              className="w-full hover:bg-[#6e56cf]/20 hover:text-white transition-all duration-200 flex items-center gap-2 py-1.5 text-slate-300"
                            >
                              <span className="h-1.5 w-1.5 rounded-full bg-[#a78bfa]/70"></span>
                              {subItem.title}
                            </Link>
                          </DropdownMenuItem>
                        ))}
                      </div>
                    </DropdownMenuContent>
                  </DropdownMenu>
                ) : (
                  <Link
                    key={item.href}
                    href={item.href}
                    className={cn(
                      "text-sm font-medium transition-all duration-300 flex items-center gap-1.5 relative group px-2 py-1",
                      "hover:text-white hover:bg-[#6e56cf]/20 rounded-md",
                      pathname === item.href
                        ? "text-white font-semibold after:absolute after:bottom-0 after:left-2 after:right-2 after:h-0.5 after:bg-[#a78bfa]/70 after:rounded-full"
                        : "text-slate-300",
                    )}
                  >
                    {item.icon && (
                      <span
                        className={cn(
                          "transition-transform duration-300 group-hover:rotate-12",
                          pathname === item.href ? "text-[#a78bfa]" : "text-slate-300",
                        )}
                      >
                        {item.icon}
                      </span>
                    )}
                    {item.title}
                  </Link>
                ),
              )}
            </nav>
          </div>

          <div className="flex items-center gap-3">

            <div className="md:hidden">
              <Sheet open={isOpen} onOpenChange={setIsOpen}>
                <SheetTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="rounded-full hover:bg-[#6e56cf]/20 text-white hover:text-white transition-all duration-300"
                  >
                    <Menu className="h-5 w-5" />
                    <span className="sr-only">Öppna meny</span>
                  </Button>
                </SheetTrigger>
                <SheetContent
                  side="left"
                  className="w-[300px] sm:w-[400px] bg-gradient-to-br from-[#1a1333]/95 to-[#2d1d57]/95 backdrop-blur-md border-[#6e56cf]/20"
                >
                  <div className="absolute inset-0 overflow-hidden pointer-events-none">
                    <div className="stars-small absolute inset-0 opacity-20"></div>
                    <div className="absolute top-0 inset-x-0 h-32 bg-gradient-to-b from-[#6e56cf]/10 to-transparent"></div>
                    <div className="absolute bottom-0 inset-x-0 h-32 bg-gradient-to-t from-[#6e56cf]/10 to-transparent"></div>
                  </div>

                  <div className="relative z-10">
                    <div className="flex items-center gap-2 mb-8 mt-2">
                      <Star className="h-6 w-6 text-[#a78bfa]" />
                      <span className="font-display text-xl cosmic-title">
                        Horoskopet.nu
                      </span>
                    </div>

                    <nav className="flex flex-col gap-5">
                      {mainNavItems.map((item) => (
                        <div key={item.href} className="space-y-3">
                          <Link
                            href={item.href}
                            onClick={() => setIsOpen(false)}
                            className="flex items-center gap-2 text-lg font-medium text-white hover:text-[#a78bfa] transition-colors"
                          >
                            <div className="bg-[#6e56cf]/30 p-1.5 rounded-md">{item.icon}</div>
                            {item.title}
                          </Link>
                          {item.submenu && (
                            <div className="grid gap-1 pl-9">
                              {item.submenu.map((subItem) => (
                                <Link
                                  key={subItem.href}
                                  href={subItem.href}
                                  onClick={() => setIsOpen(false)}
                                  className="text-slate-300 hover:text-white py-1.5 flex items-center gap-2 transition-all duration-200 hover:translate-x-1"
                                >
                                  <span className="h-1.5 w-1.5 rounded-full bg-[#a78bfa]/70"></span>
                                  {subItem.title}
                                </Link>
                              ))}
                            </div>
                          )}
                        </div>
                      ))}
                      <div className="border-t border-[#6e56cf]/20 pt-4 mt-4">
                        <Link
                          href="/om-oss"
                          onClick={() => setIsOpen(false)}
                          className="flex items-center gap-2 text-slate-300 hover:text-white py-2 transition-colors"
                        >
                          <Info className="h-4 w-4" />
                          Om Oss
                        </Link>
                        <Link
                          href="/kontakt"
                          onClick={() => setIsOpen(false)}
                          className="flex items-center gap-2 text-slate-300 hover:text-white py-2 transition-colors"
                        >
                          <Info className="h-4 w-4" />
                          Kontakt
                        </Link>
                      </div>
                    </nav>
                  </div>
                </SheetContent>
              </Sheet>
            </div>

            <Button
              variant="default"
              size="sm"
              className="bg-gradient-to-r from-[#6e56cf]/80 to-[#a78bfa]/80 hover:from-[#6e56cf]/90 hover:to-[#a78bfa]/90 text-white border border-[#a78bfa]/30 shadow-md shadow-[#6e56cf]/20 hover:shadow-lg hover:shadow-[#6e56cf]/30 transition-all duration-300 group"
              asChild
            >
              <Link href="/horoskop/dagens">
                <Star className="h-3.5 w-3.5 mr-1.5 animate-twinkle" />
                Dagens Horoskop
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </header>
  )
}
