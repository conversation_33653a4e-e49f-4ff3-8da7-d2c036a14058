"use client"

import { useState } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import Image from "next/image"

export function FamousCouples() {
  const [activeTab, setActiveTab] = useState("lasting")

  const lastingCouples = [
    {
      names: "Victoria och Daniel",
      signs: "Kräftan & Skorpionen",
      description:
        "Kronprinsessan Victoria (Kräftan) och prins <PERSON> (Skorpionen) visar den starka kompatibiliteten mellan vattentecken. Deras relation präglas av emotionellt djup, lojalitet och ömsesidig förståelse.",
      image: "/placeholder-dgmae.png",
    },
    {
      names: "<PERSON><PERSON><PERSON> och <PERSON>",
      signs: "Skytten & Jungfrun",
      description:
        "<PERSON><PERSON><PERSON> (Skytten) och <PERSON> (Jungfrun) representerar en intressant kombination av eld och jord. Trots utmaningar har deras relation överlevt genom att balansera Skyttens äventyrslust med Jungfruns praktiska stabilitet.",
      image: "/placeholder-yfg7v.png",
    },
    {
      names: "<PERSON> och <PERSON> Wilson",
      signs: "Kräftan & Skorpionen",
      description:
        "Tom Hanks (Kräftan) och Rita Wilson (Skorpionen) är ytterligare ett exempel på den starka kompatibiliteten mellan vattentecken. Deras långvariga äktenskap visar på den djupa emotionella koppling som kan finnas mellan dessa tecken.",
      image: "/placeholder-t7lcf.png",
    },
  ]

  const passionateCouples = [
    {
      names: "Johnny Depp och Winona Ryder",
      signs: "Tvillingarna & Skorpionen",
      description:
        "Johnny Depp (Tvillingarna) och Winona Ryder (Skorpionen) hade en intensiv relation som visar den magnetiska attraktionen mellan luft och vatten. Deras relation var passionerad men utmanande på grund av deras olika emotionella behov.",
      image: "/placeholder-gbqhu.png",
    },
    {
      names: "Brad Pitt och Angelina Jolie",
      signs: "Skytten & Tvillingarna",
      description:
        "Brad Pitt (Skytten) och Angelina Jolie (Tvillingarna) representerade en dynamisk kombination av eld och luft. Deras relation var intensiv och passionerad, med stark intellektuell och fysisk attraktion.",
      image: "/placeholder-ylxno.png",
    },
    {
      names: "Elizabeth Taylor och Richard Burton",
      signs: "Fiskarna & Skorpionen",
      description:
        "Elizabeth Taylor (Fiskarna) och Richard Burton (Skorpionen) hade en av Hollywoods mest passionerade och stormiga relationer. Deras vattentecken skapade en djup emotionell och intuitiv koppling som var både intensiv och utmanande.",
      image: "/placeholder.svg?height=200&width=300&query=Elizabeth Taylor och Richard Burton",
    },
  ]

  const surprisingCouples = [
    {
      names: "David och Victoria Beckham",
      signs: "Väduren & Väduren",
      description:
        "David och Victoria Beckham är båda Väduren, vilket kan skapa både stark attraktion och utmaningar. Deras framgångsrika äktenskap visar att två starka personligheter kan fungera tillsammans när de respekterar varandras ambitioner.",
      image: "/placeholder.svg?height=200&width=300&query=David och Victoria Beckham",
    },
    {
      names: "Barack och Michelle Obama",
      signs: "Lejonet & Vattumannen",
      description:
        "Barack (Lejonet) och Michelle Obama (Vattumannen) representerar motsatta tecken i zodiaken, vilket kan skapa en dynamisk balans. Deras relation visar hur motsatser kan komplettera varandra när det finns ömsesidig respekt.",
      image: "/placeholder.svg?height=200&width=300&query=Barack och Michelle Obama",
    },
    {
      names: "Prince William och Kate Middleton",
      signs: "Kräftan & Stenbocken",
      description:
        "Prince William (Kräftan) och Kate Middleton (Stenbocken) är också motsatta tecken, vilket skapar en naturlig balans. Kräftans emotionella djup kompletterar Stenbockens praktiska ambition.",
      image: "/placeholder.svg?height=200&width=300&query=Prince William och Kate Middleton",
    },
  ]

  return (
    <div>
      <Tabs defaultValue="lasting" value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-3 mb-8">
          <TabsTrigger value="lasting">Långvariga par</TabsTrigger>
          <TabsTrigger value="passionate">Passionerade par</TabsTrigger>
          <TabsTrigger value="surprising">Överraskande par</TabsTrigger>
        </TabsList>

        <TabsContent value="lasting" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {lastingCouples.map((couple, index) => (
              <Card key={index} className="overflow-hidden">
                <div className="relative h-48">
                  <Image src={couple.image || "/placeholder.svg"} alt={couple.names} fill className="object-cover" />
                </div>
                <CardContent className="p-4">
                  <h3 className="font-heading text-lg mb-1 text-cosmic-purple-600 dark:text-cosmic-purple-400">
                    {couple.names}
                  </h3>
                  <p className="text-sm font-medium mb-2">{couple.signs}</p>
                  <p className="text-sm">{couple.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="passionate" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {passionateCouples.map((couple, index) => (
              <Card key={index} className="overflow-hidden">
                <div className="relative h-48">
                  <Image src={couple.image || "/placeholder.svg"} alt={couple.names} fill className="object-cover" />
                </div>
                <CardContent className="p-4">
                  <h3 className="font-heading text-lg mb-1 text-cosmic-purple-600 dark:text-cosmic-purple-400">
                    {couple.names}
                  </h3>
                  <p className="text-sm font-medium mb-2">{couple.signs}</p>
                  <p className="text-sm">{couple.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="surprising" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {surprisingCouples.map((couple, index) => (
              <Card key={index} className="overflow-hidden">
                <div className="relative h-48">
                  <Image src={couple.image || "/placeholder.svg"} alt={couple.names} fill className="object-cover" />
                </div>
                <CardContent className="p-4">
                  <h3 className="font-heading text-lg mb-1 text-cosmic-purple-600 dark:text-cosmic-purple-400">
                    {couple.names}
                  </h3>
                  <p className="text-sm font-medium mb-2">{couple.signs}</p>
                  <p className="text-sm">{couple.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
