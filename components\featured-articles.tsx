import Link from "next/link"
import Image from "next/image"
import { Calendar, Clock } from "lucide-react"
import { <PERSON>, CardContent, CardFooter } from "@/components/ui/card"

const articles = [
  {
    title: "Fullmåne i Skorpionen: Så påverkas du",
    slug: "fullmane-maj-2025-skorpionen",
    excerpt:
      "Upptäck hur fullmånen i Skorpionen påverkar ditt stjärntecken och vad du kan förvänta dig under denna intensiva period.",
    date: "2025-05-10",
    readTime: "5 min",
    image: "fullmoon in scorpio with mystical purple glow",
  },
  {
    title: "Venus retrograd: Kärlek på paus?",
    slug: "venus-retrograd-karlek-pa-paus",
    excerpt: "Venus går retrograd och kan påverka dina relationer. Läs om hur du kan navigera denna utmanande period.",
    date: "2025-05-05",
    readTime: "4 min",
    image: "venus planet with retrograde symbol",
  },
  {
    title: "Merkurius i Tvillingarna: Kommunikation i fokus",
    slug: "merkurius-tvillingarna-kommunikation",
    excerpt:
      "Merkurius går in i sitt hemtecken Tvillingarna. Lär dig hur du kan dra nytta av denna period för bättre kommunikation.",
    date: "2025-04-28",
    readTime: "3 min",
    image: "mercury planet with gemini symbol",
  },
]

export default function FeaturedArticles() {
  return (
    <div className="grid gap-6 md:grid-cols-3">
      {articles.map((article) => (
        <Card key={article.slug} className="overflow-hidden">
          <div className="relative h-48">
            <Image
              src={`/abstract-geometric-shapes.png?key=cghbl&height=192&width=384&query=${article.image}`}
              alt={article.title}
              fill
              className="object-cover"
            />
          </div>
          <CardContent className="p-4">
            <div className="flex items-center gap-3 text-xs text-muted-foreground mb-2">
              <div className="flex items-center">
                <Calendar className="h-3 w-3 mr-1" />
                {article.date}
              </div>
              <div className="flex items-center">
                <Clock className="h-3 w-3 mr-1" />
                {article.readTime}
              </div>
            </div>
            <h3 className="font-semibold text-lg mb-2">
              <Link href={`/aktuellt/${article.slug}`} className="hover:underline">
                {article.title}
              </Link>
            </h3>
            <p className="text-sm text-muted-foreground line-clamp-3">{article.excerpt}</p>
          </CardContent>
          <CardFooter className="p-4 pt-0">
            <Link href={`/aktuellt/${article.slug}`} className="text-sm font-medium hover:underline">
              Läs mer
            </Link>
          </CardFooter>
        </Card>
      ))}
    </div>
  )
}
