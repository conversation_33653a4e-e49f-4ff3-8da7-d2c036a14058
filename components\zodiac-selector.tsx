import Link from "next/link"
import Image from "next/image"

const zodiacSigns = [
  { name: "<PERSON><PERSON><PERSON><PERSON>", path: "vaduren", dates: "21 mar - 19 apr" },
  { name: "Oxen", path: "oxen", dates: "20 apr - 20 maj" },
  { name: "<PERSON><PERSON><PERSON><PERSON><PERSON>", path: "tvil<PERSON><PERSON><PERSON>", dates: "21 maj - 20 jun" },
  { name: "<PERSON><PERSON><PERSON><PERSON><PERSON>", path: "kraft<PERSON>", dates: "21 jun - 22 jul" },
  { name: "<PERSON><PERSON><PERSON>", path: "lejonet", dates: "23 jul - 22 aug" },
  { name: "<PERSON><PERSON><PERSON>", path: "jungfrun", dates: "23 aug - 22 sep" },
  { name: "<PERSON><PERSON><PERSON>", path: "vagen", dates: "23 sep - 22 okt" },
  { name: "<PERSON>korpion<PERSON>", path: "skorpionen", dates: "23 okt - 21 nov" },
  { name: "Sky<PERSON>", path: "skytten", dates: "22 nov - 21 dec" },
  { name: "<PERSON><PERSON><PERSON><PERSON><PERSON>", path: "stenbo<PERSON>n", dates: "22 dec - 19 jan" },
  { name: "<PERSON><PERSON><PERSON><PERSON>", path: "vattuman<PERSON>", dates: "20 jan - 18 feb" },
  { name: "<PERSON><PERSON><PERSON>", path: "fiskarna", dates: "19 feb - 20 mar" },
];

function getZodiacImage(name: string): string {
  switch (name) {
    case "Väduren": return "aries.jpg";
    case "Oxen": return "taurus.jpg";
    case "Tvillingarna": return "gemini.jpg";
    case "Kräftan": return "cancer.jpg";
    case "Lejonet": return "leo.jpg";
    case "Jungfrun": return "virgo.jpg";
    case "Vågen": return "libra.jpg";
    case "Skorpionen": return "scorpio.jpg";
    case "Skytten": return "sagittarius.jpg";
    case "Stenbocken": return "capricorn.jpg";
    case "Vattumannen": return "aquarius.jpg";
    case "Fiskarna": return "pisces.jpg";
    default: return "placeholder.jpg";
  }
}

export default function ZodiacSelector() {
  return (
    <section className="rounded-2xl bg-gradient-to-br from-cosmic-700/30 to-stardust-400/20 p-6 md:p-10 shadow-xl mb-8 border border-cosmic-200/20">
      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-6">
        {zodiacSigns.map((sign) => (
          <Link
            key={sign.path}
            href={`/horoskop/dagens/${sign.path}`}
            className="flex flex-col items-center p-4 rounded-xl bg-white/70 dark:bg-cosmic-950/60 shadow-md border border-cosmic-100/30 hover:shadow-lg hover:-translate-y-1 hover:bg-cosmic-100/30 transition-all duration-200 group"
            style={{ minHeight: 160 }}
          >
            <div className="relative w-16 h-16 mb-3">
              <div className="absolute inset-0 rounded-full ring-2 ring-cosmic-400/60 group-hover:ring-cosmic-600/80 ring-offset-2 ring-offset-white dark:ring-offset-cosmic-950 transition-all"></div>
              <Image
                src={`/images/${getZodiacImage(sign.name)}`}
                alt={`${sign.name} symbol`}
                fill
                className="object-contain rounded-full"
                sizes="64px"
              />
            </div>
            <span className="font-semibold text-base text-cosmic-800 dark:text-cosmic-100 drop-shadow-sm mb-0.5 group-hover:text-cosmic-600 transition-colors">{sign.name}</span>
            <span className="text-xs text-muted-foreground dark:text-cosmic-300">{sign.dates}</span>
          </Link>
        ))}
      </div>
    </section>
  )
}
