const fs = require('fs')
const path = require('path')

// Funktion för att kontrollera om en page.tsx fil innehåller redirect
function isRedirectPage(filePath) {
  try {
    if (!fs.existsSync(filePath)) {
      return false
    }
    
    const content = fs.readFileSync(filePath, 'utf8')
    const hasRedirect = content.includes('redirect(')
    const hasImport = content.includes('from "next/navigation"') || content.includes("from 'next/navigation'")
    return hasRedirect && hasImport
  } catch (error) {
    console.error(`Error reading file ${filePath}:`, error)
    return false
  }
}

// Funktion för att extrahera redirect-destination från fil
function getRedirectDestination(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8')
    const redirectMatch = content.match(/redirect\(['"`]([^'"`]+)['"`]\)/)
    return redirectMatch ? redirectMatch[1] : null
  } catch (error) {
    console.error(`Error reading file ${filePath}:`, error)
    return null
  }
}

// Funktion för att hitta alla redirect-sidor
function findAllRedirectPages(dir, basePath = '') {
  const redirectPages = []
  
  try {
    const items = fs.readdirSync(dir, { withFileTypes: true })
    
    for (const item of items) {
      const fullPath = path.join(dir, item.name)
      const urlPath = path.join(basePath, item.name).replace(/\\/g, '/')
      
      if (item.isDirectory()) {
        const pagePath = path.join(fullPath, 'page.tsx')
        if (fs.existsSync(pagePath) && isRedirectPage(pagePath)) {
          const destination = getRedirectDestination(pagePath)
          redirectPages.push({
            source: urlPath === '' ? '/' : `/${urlPath}`,
            destination: destination,
            filePath: pagePath
          })
        }
        
        redirectPages.push(...findAllRedirectPages(fullPath, urlPath))
      }
    }
  } catch (error) {
    console.error(`Error reading directory ${dir}:`, error)
  }
  
  return redirectPages
}

// Huvudfunktion
function checkRedirects() {
  console.log('🔍 Söker efter redirect-sidor...\n')
  
  const appDir = path.join(process.cwd(), 'app')
  const redirectPages = findAllRedirectPages(appDir)
  
  if (redirectPages.length === 0) {
    console.log('✅ Inga redirect-sidor hittades.')
    return
  }
  
  console.log(`📋 Hittade ${redirectPages.length} redirect-sidor:\n`)
  
  redirectPages.forEach((redirect, index) => {
    console.log(`${index + 1}. ${redirect.source}`)
    console.log(`   → ${redirect.destination}`)
    console.log(`   📁 ${redirect.filePath}`)
    console.log('')
  })
  
  console.log('💡 Dessa sidor exkluderas automatiskt från sitemap för att undvika 3XX redirects.')
  console.log('💡 Endast destination-URL:erna inkluderas i sitemap.')
}

// Kör kontrollen
checkRedirects()
