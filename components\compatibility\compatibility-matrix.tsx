"use client"

import { useState, useRef, useEffect } from "react"
import { compatibilityData } from "@/lib/compatibility-data"
import { zodiacData } from "@/lib/zodiac-data"
import Link from "next/link"

export function CompatibilityMatrix() {
  const [hoveredCell, setHoveredCell] = useState<string | null>(null)
  const timeoutRef = useRef<NodeJS.Timeout | null>(null)

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
    }
  }, [])

  // Helper function to get zodiac sign path by name
  const getSignPath = (signName: string) => {
    const sign = zodiacData.find(s => s.name === signName)
    return sign?.path || ""
  }

  // Handle mouse enter with immediate show
  const handleMouseEnter = (cellId: string) => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
      timeoutRef.current = null
    }
    setHoveredCell(cellId)
  }

  // <PERSON>le mouse leave with delay
  const handleMouseLeave = () => {
    timeoutRef.current = setTimeout(() => {
      setHoveredCell(null)
    }, 300) // 300ms delay
  }

  // Handle tooltip mouse enter (keep tooltip visible)
  const handleTooltipMouseEnter = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
      timeoutRef.current = null
    }
  }

  // Handle tooltip mouse leave (hide after delay)
  const handleTooltipMouseLeave = () => {
    timeoutRef.current = setTimeout(() => {
      setHoveredCell(null)
    }, 150) // Shorter delay when leaving tooltip
  }

  const getCompatibilityColor = (score: number) => {
    if (score >= 85) return "bg-emerald-500/20 text-emerald-300 border-emerald-500/30"
    if (score >= 70) return "bg-[#a78bfa]/20 text-[#a78bfa] border-[#a78bfa]/30"
    if (score >= 60) return "bg-amber-500/20 text-amber-300 border-amber-500/30"
    if (score >= 50) return "bg-orange-500/20 text-orange-300 border-orange-500/30"
    return "bg-red-500/20 text-red-300 border-red-500/30"
  }

  const getCompatibilityLabel = (score: number) => {
    if (score >= 85) return "Utmärkt"
    if (score >= 70) return "Mycket bra"
    if (score >= 60) return "Bra"
    if (score >= 50) return "Neutral"
    return "Utmanande"
  }

  const getCompatibilityScore = (sign1: string, sign2: string) => {
    const key1 = `${sign1}-${sign2}`
    const key2 = `${sign2}-${sign1}`

    if (compatibilityData[key1]) {
      return compatibilityData[key1].score
    } else if (compatibilityData[key2]) {
      return compatibilityData[key2].score
    }

    // Fallback for same sign
    if (sign1 === sign2) {
      return 75 // Generally good compatibility with same sign
    }

    return 50 // Default neutral score
  }

  const getCompatibilityDescription = (sign1: string, sign2: string) => {
    const key1 = `${sign1}-${sign2}`
    const key2 = `${sign2}-${sign1}`

    if (compatibilityData[key1]) {
      return compatibilityData[key1].shortDescription
    } else if (compatibilityData[key2]) {
      return compatibilityData[key2].shortDescription
    }

    // Fallback for same sign
    if (sign1 === sign2) {
      return `${sign1} förstår varandra på en djup nivå, men kan förstärka både positiva och negativa egenskaper.`
    }

    return `Kompatibilitet mellan ${sign1} och ${sign2}.`
  }

  return (
    <div className="overflow-x-auto">
      <table className="min-w-full border-collapse">
        <thead>
          <tr>
            <th className="p-2 border border-[#6e56cf]/30 bg-[#0c0817]/50"></th>
            {zodiacData.map((sign) => (
              <th
                key={sign.name}
                className="p-2 border border-[#6e56cf]/30 bg-[#0c0817]/50 text-xs md:text-sm font-medium text-white"
              >
                {sign.name}
              </th>
            ))}
          </tr>
        </thead>
        <tbody>
          {zodiacData.map((rowSign) => (
            <tr key={rowSign.name}>
              <th className="p-2 border border-[#6e56cf]/30 bg-[#0c0817]/50 text-xs md:text-sm font-medium text-white">
                {rowSign.name}
              </th>
              {zodiacData.map((colSign) => {
                const score = getCompatibilityScore(rowSign.name, colSign.name)
                const cellId = `${rowSign.name}-${colSign.name}`
                const isHovered = hoveredCell === cellId

                return (
                  <td
                    key={cellId}
                    className={`p-1 border border-[#6e56cf]/30 text-center relative ${
                      isHovered ? "ring-2 ring-[#a78bfa]" : ""
                    }`}
                    onMouseEnter={() => handleMouseEnter(cellId)}
                    onMouseLeave={handleMouseLeave}
                  >
                    <Link
                      href={`/relationer/${rowSign.path}/${colSign.path}`}
                      className={`block w-full h-full p-2 text-sm font-medium transition-all duration-200 hover:scale-105 ${getCompatibilityColor(score)} hover:shadow-lg rounded-sm`}
                      title={`${rowSign.name} + ${colSign.name}: ${getCompatibilityLabel(score)} (${score}%)`}
                    >
                      {score}
                    </Link>

                    {/* Tooltip on hover */}
                    {isHovered && (
                      <div
                        className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-[#1a1333] border border-[#6e56cf]/30 rounded-lg shadow-lg opacity-100 transition-opacity duration-200 z-20 min-w-max"
                        onMouseEnter={handleTooltipMouseEnter}
                        onMouseLeave={handleTooltipMouseLeave}
                      >
                        <Link
                          href={`/relationer/${rowSign.path}/${colSign.path}`}
                          className="block text-center hover:bg-[#6e56cf]/10 rounded p-1 transition-colors"
                        >
                          <p className="font-bold mb-1 text-white text-sm">
                            {rowSign.name} + {colSign.name}
                          </p>
                          <p className="mb-2">
                            <span
                              className={`inline-block px-2 py-0.5 rounded-full text-xs ${
                                score >= 85
                                  ? "bg-emerald-500/20 text-emerald-300"
                                  : score >= 70
                                    ? "bg-[#a78bfa]/20 text-[#a78bfa]"
                                    : score >= 60
                                      ? "bg-amber-500/20 text-amber-300"
                                      : score >= 50
                                        ? "bg-orange-500/20 text-orange-300"
                                        : "bg-red-500/20 text-red-300"
                              }`}
                            >
                              {getCompatibilityLabel(score)}
                            </span>
                          </p>
                          <p className="text-xs text-slate-300 max-w-xs">
                            {getCompatibilityDescription(rowSign.name, colSign.name)}
                          </p>
                          <p className="text-xs text-[#a78bfa] mt-2 font-medium">Klicka för fullständig analys →</p>
                        </Link>
                        {/* Arrow */}
                        <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-[#1a1333]"></div>
                      </div>
                    )}
                  </td>
                )
              })}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  )
}
