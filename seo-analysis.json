{"pages": [{"url": "/aktuellt", "filePath": "C:\\Dev\\horoskopetnew\\app\\aktuellt\\page.tsx", "hasMetadata": true, "hasTitle": true, "hasDescription": true, "hasCanonical": true, "isDynamic": false, "title": "Aktuella astrologiska händelser | Horoskopet.nu", "description": "<PERSON><PERSON>ll dig uppdaterad med de senaste kosmiska händelserna, planetrörelser och astrologiska transiter. Lär dig hur de påverkar ditt liv och stjärntecken.", "titleLength": 47, "descriptionLength": 150}, {"url": "/aktuellt/fullmane-maj-2025-skorpionen", "filePath": "C:\\Dev\\horoskopetnew\\app\\aktuellt\\fullmane-maj-2025-skorpionen\\page.tsx", "hasMetadata": true, "hasTitle": true, "hasDescription": true, "hasCanonical": true, "isDynamic": false, "title": "Fullmåne i Skorpionen maj 2025 | Horoskopet.nu", "description": "<PERSON><PERSON>r dig om fullmånens kraftfulla energier i Skorpionen 15 maj 2025. <PERSON>pt<PERSON><PERSON> hur denna lunation p<PERSON><PERSON><PERSON> transformation, käns<PERSON> och personlig utveckling för alla stjärntecken.", "titleLength": 46, "descriptionLength": 175}, {"url": "/aktuellt/jupiter-lejonet-2025", "filePath": "C:\\Dev\\horoskopetnew\\app\\aktuellt\\jupiter-lejonet-2025\\page.tsx", "hasMetadata": true, "hasTitle": true, "hasDescription": true, "hasCanonical": true, "isDynamic": false, "title": "Jupiter i Lejonet 2025 | Horoskopet.nu", "description": "Upptäck hur Jupiter i Lejonet från juli 2025 till maj 2026 påverkar kreativitet, ledarskap och personlig tillväxt. Lär dig hur alla stjärntecken kan dra nytta av denna kraftfulla transit.", "titleLength": 38, "descriptionLength": 187}, {"url": "/aktuellt/merkurius-retrograd-maj-2025", "filePath": "C:\\Dev\\horoskopetnew\\app\\aktuellt\\merkurius-retrograd-maj-2025\\page.tsx", "hasMetadata": true, "hasTitle": true, "hasDescription": true, "hasCanonical": true, "isDynamic": false, "title": "Merkurius retrograd maj 2025 | Horoskopet.nu", "description": "<PERSON><PERSON><PERSON> dig om Merkurius retrograd 15 maj - 7 juni 2025. <PERSON><PERSON><PERSON>ck hur denna period påverkar kommunikation, teknik och resor för alla stjärntecken. Få tips för att navigera retrograden.", "titleLength": 44, "descriptionLength": 179}, {"url": "/aktuellt/merkurius-tvillingarna-kommunikation", "filePath": "C:\\Dev\\horoskopetnew\\app\\aktuellt\\merkurius-tvillingarna-kommunikation\\page.tsx", "hasMetadata": true, "hasTitle": true, "hasDescription": true, "hasCanonical": true, "isDynamic": false, "title": "Merkurius i Tvillingarna 2025 | Horoskopet.nu", "description": "Upptäck hur Merkurius i sitt hemtecken Tvillingarna förbättrar kommunikation, lärande och sociala kontakter. Lär dig hur alla stjärntecken kan dra nytta av denna gynnsamma period.", "titleLength": 45, "descriptionLength": 179}, {"url": "/aktuellt/nymane-kraftan-juli-2025", "filePath": "C:\\Dev\\horoskopetnew\\app\\aktuellt\\nymane-kraftan-juli-2025\\page.tsx", "hasMetadata": true, "hasTitle": true, "hasDescription": true, "hasCanonical": true, "isDynamic": false, "title": "Nymåne i Kräftan juli 2025 | Horoskopet.nu", "description": "Upptäck kraften i nymånen i Kräftan 17 juli 2025. <PERSON><PERSON><PERSON> dig att sätta intentioner för hem, familj och emotionellt välbefinnande. Ritualer och tips för alla stjärntecken.", "titleLength": 42, "descriptionLength": 167}, {"url": "/aktuellt/sommarsolstandet-2025", "filePath": "C:\\Dev\\horoskopetnew\\app\\aktuellt\\sommarsolstandet-2025\\page.tsx", "hasMetadata": true, "hasTitle": true, "hasDescription": true, "hasCanonical": true, "isDynamic": false, "title": "Sommarsolståndet 2025 | Horoskopet.nu", "description": "Upptäck den astrologiska betydelsen av sommarsolståndet 21 juni 2025. <PERSON><PERSON><PERSON> dig om solens kraft, Kräftans energi och hur alla stjärntecken påverkas av årets längsta dag.", "titleLength": 37, "descriptionLength": 167}, {"url": "/aktuellt/venus-retrograd-karlek-pa-paus", "filePath": "C:\\Dev\\horoskopetnew\\app\\aktuellt\\venus-retrograd-karlek-pa-paus\\page.tsx", "hasMetadata": true, "hasTitle": true, "hasDescription": true, "hasCanonical": true, "isDynamic": false, "title": "Venus retrograd 2025 | Horoskopet.nu", "description": "<PERSON><PERSON><PERSON> dig om Venus retrograd och hur den på<PERSON> kärle<PERSON>, relationer och värderingar. <PERSON><PERSON> råd för att navigera denna utmanande period och förstå dess astrologiska betydelse.", "titleLength": 36, "descriptionLength": 170}, {"url": "/aktuellt/venus-tvillingarna-juni-2025", "filePath": "C:\\Dev\\horoskopetnew\\app\\aktuellt\\venus-tvillingarna-juni-2025\\page.tsx", "hasMetadata": true, "hasTitle": true, "hasDescription": true, "hasCanonical": true, "isDynamic": false, "title": "Venus i Tvillingarna juni 2025 | Horoskopet.nu", "description": "Upptäck hur Venus i Tvillingarna påverkar kärlek och relationer juni 2025. <PERSON><PERSON>r dig om kommunikationens roll i kärleken och hur alla stjärntecken påverkas av denna transit.", "titleLength": 46, "descriptionLength": 171}, {"url": "/astrologi-lara", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\page.tsx", "hasMetadata": true, "hasTitle": true, "hasDescription": true, "hasCanonical": true, "isDynamic": false, "title": "Lär dig astrologi - Kunskapsbank | Horoskopet.nu", "description": "Utforska astrologins värld med vår omfattande kunskapsbank. Lär dig om planeter, hus, as<PERSON><PERSON><PERSON>, stjärntecken och astrologins grundläggande principer.", "titleLength": 48, "descriptionLength": 149}, {"url": "/astrologi-lara/aspekter", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\aspekter\\page.tsx", "hasMetadata": true, "hasTitle": true, "hasDescription": true, "hasCanonical": true, "isDynamic": false, "title": "Astrologiska aspekter - Planeternas relationer | Horoskopet.nu", "description": "<PERSON><PERSON>r dig om astrologiska aspekter - vinklarna mellan planeter som skapar energimönster i ditt horoskop. Upptäck konjunktioner, trigoner, k<PERSON>draturer och mer.", "titleLength": 62, "descriptionLength": 156}, {"url": "/astrologi-lara/aspekter/biquintil", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\aspekter\\biquintil\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/astrologi-lara/aspekter/halvkvadrat", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\aspekter\\halvkvadrat\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/astrologi-lara/aspekter/introduktion", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\aspekter\\introduktion\\page.tsx", "hasMetadata": true, "hasTitle": true, "hasDescription": true, "hasCanonical": false, "isDynamic": false, "title": "Introduktion till astrologiska aspekter | Horoskopet.nu", "description": "<PERSON><PERSON><PERSON> dig grunderna om astrologiska aspekter, deras betyd<PERSON>e och hur de påverkar ditt horoskop.", "titleLength": 55, "descriptionLength": 94}, {"url": "/astrologi-lara/aspekter/konjunktion", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\aspekter\\konjunktion\\page.tsx", "hasMetadata": true, "hasTitle": true, "hasDescription": true, "hasCanonical": false, "isDynamic": false, "title": "Konjunktion i astrologi | Horoskopet.nu", "description": "<PERSON><PERSON><PERSON> dig om konjunktionen, den kraftfulla 0-graders aspekten i astrologi, och hur den påverkar ditt horoskop.", "titleLength": 39, "descriptionLength": 108}, {"url": "/astrologi-lara/aspekter/kvadratur", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\aspekter\\kvadratur\\page.tsx", "hasMetadata": true, "hasTitle": true, "hasDescription": true, "hasCanonical": false, "isDynamic": false, "title": "Kvadratur i astrologi | Horoskopet.nu", "description": "<PERSON><PERSON><PERSON> dig om k<PERSON>draturen, den utmanande 90-graders aspekten i astrologi, och hur den påverkar ditt horoskop.", "titleLength": 37, "descriptionLength": 106}, {"url": "/astrologi-lara/aspekter/kvinkuns", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\aspekter\\kvinkuns\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/astrologi-lara/aspekter/kvintil", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\aspekter\\kvintil\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/astrologi-lara/aspekter/monster", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\aspekter\\monster\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/astrologi-lara/aspekter/monster/grand-cross", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\aspekter\\monster\\grand-cross\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/astrologi-lara/aspekter/monster/gyllene-yod", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\aspekter\\monster\\gyllene-yod\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/astrologi-lara/aspekter/monster/kite", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\aspekter\\monster\\kite\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/astrologi-lara/aspekter/monster/mystiska-rektangeln", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\aspekter\\monster\\mystiska-rektangeln\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/astrologi-lara/aspekter/monster/stellium", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\aspekter\\monster\\stellium\\page.tsx", "hasMetadata": true, "hasTitle": true, "hasDescription": true, "hasCanonical": false, "isDynamic": false, "title": "Stellium (Planetanhopning) - Aspektmönster | Horoskopet.nu", "description": "<PERSON><PERSON>r dig om stellium, en kraftfull koncentration av tre eller fler planeter i samma tecken eller hus som skapar intensivt fokus inom specifika livsområden.", "titleLength": 58, "descriptionLength": 154}, {"url": "/astrologi-lara/aspekter/monster/stortrigon", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\aspekter\\monster\\stortrigon\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/astrologi-lara/aspekter/monster/t-kvadratur", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\aspekter\\monster\\t-kvadratur\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/astrologi-lara/aspekter/monster/thors-hammare", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\aspekter\\monster\\thors-hammare\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/astrologi-lara/aspekter/monster/visualisering", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\aspekter\\monster\\visualisering\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/astrologi-lara/aspekter/monster/yod", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\aspekter\\monster\\yod\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/astrologi-lara/aspekter/opposition", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\aspekter\\opposition\\page.tsx", "hasMetadata": true, "hasTitle": true, "hasDescription": true, "hasCanonical": false, "isDynamic": false, "title": "Opposition i astrologi | Horoskopet.nu", "description": "<PERSON><PERSON><PERSON> dig om oppositionen, den utmanande 180-graders as<PERSON><PERSON>en i astrologi, och hur den påverkar ditt horoskop.", "titleLength": 38, "descriptionLength": 108}, {"url": "/astrologi-lara/aspekter/semisextil", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\aspekter\\semisextil\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/astrologi-lara/aspekter/seskvikvadratur", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\aspekter\\seskvikvadratur\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/astrologi-lara/aspekter/sextil", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\aspekter\\sextil\\page.tsx", "hasMetadata": true, "hasTitle": true, "hasDescription": true, "hasCanonical": false, "isDynamic": false, "title": "Sextil i astrologi | Horoskopet.nu", "description": "<PERSON><PERSON><PERSON> dig om sextilen, den harmoniska 60-graders aspekten i astrologi, och hur den påverkar ditt horoskop.", "titleLength": 34, "descriptionLength": 104}, {"url": "/astrologi-lara/aspekter/trigon", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\aspekter\\trigon\\page.tsx", "hasMetadata": true, "hasTitle": true, "hasDescription": true, "hasCanonical": false, "isDynamic": false, "title": "Trigon i astrologi | Horoskopet.nu", "description": "<PERSON><PERSON><PERSON> dig om trigonen, den harmoniska 120-graders aspekten i astrologi, och hur den påverkar ditt horoskop.", "titleLength": 34, "descriptionLength": 105}, {"url": "/astrologi-lara/grunderna", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\grunderna\\page.tsx", "hasMetadata": true, "hasTitle": true, "hasDescription": true, "hasCanonical": true, "isDynamic": false, "title": "Grunderna i astrologi - Element, kvaliteter och mer | Horoskopet.nu", "description": "Lär dig astrologins grundläggande principer. Upptäck element, kvaliteter, zodiakens symbolik och hur födelsehoroskop skapas. Perfekt för nybörjare.", "titleLength": 67, "descriptionLength": 147}, {"url": "/astrologi-lara/grunderna/element-och-kvaliteter", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\grunderna\\element-och-kvaliteter\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": true, "hasCanonical": false, "isDynamic": false, "title": null, "description": "Eldelement representerar energi, passion och kreativitet. Personer med starka eldelement i sitt horoskop tenderar att vara entusiastiska, modiga och självständiga.", "titleLength": 0, "descriptionLength": 163}, {"url": "/astrologi-lara/grunderna/fodelsehoroskop", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\grunderna\\fodelsehoroskop\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": true, "hasCanonical": false, "isDynamic": false, "title": null, "description": "Representerar din personlighet, fysiska kropp och hur du presenterar dig för världen. Det är tecknet som steg upp över horisonten vid din födelse.", "titleLength": 0, "descriptionLength": 146}, {"url": "/astrologi-lara/grunderna/vad-ar-astrologi", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\grunderna\\vad-ar-astrologi\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/astrologi-lara/grunderna/zodiakens-symbolik", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\grunderna\\zodiakens-symbolik\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/astrologi-lara/husen", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\husen\\page.tsx", "hasMetadata": true, "hasTitle": true, "hasDescription": true, "hasCanonical": true, "isDynamic": false, "title": "De 12 astrologiska husen - Livsområden i horoskopet | Horoskopet.nu", "description": "<PERSON><PERSON>r dig om de 12 astrologiska husen som representerar olika livsområden. Upptäck hur husen visar var planeternas energier kommer till uttryck i ditt liv.", "titleLength": 67, "descriptionLength": 153}, {"url": "/astrologi-lara/husen/andra-huset", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\husen\\andra-huset\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/astrologi-lara/husen/angulara-hus", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\husen\\angulara-hus\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/astrologi-lara/husen/attonde-huset", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\husen\\attonde-huset\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/astrologi-lara/husen/derivativa-hus", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\husen\\derivativa-hus\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/astrologi-lara/husen/elfte-huset", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\husen\\elfte-huset\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/astrologi-lara/husen/femte-huset", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\husen\\femte-huset\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/astrologi-lara/husen/fjarde-huset", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\husen\\fjarde-huset\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/astrologi-lara/husen/forsta-huset", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\husen\\forsta-huset\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/astrologi-lara/husen/husspetsar", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\husen\\husspetsar\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/astrologi-lara/husen/hussystem", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\husen\\hussystem\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/astrologi-lara/husen/intercepterade-hus", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\husen\\intercepterade-hus\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/astrologi-lara/husen/introduktion", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\husen\\introduktion\\page.tsx", "hasMetadata": true, "hasTitle": true, "hasDescription": true, "hasCanonical": false, "isDynamic": false, "title": "Introduktion till de astrologiska husen | Horoskopet.nu", "description": "<PERSON><PERSON><PERSON> dig grunderna om de tolv astrologiska husen, deras betydels<PERSON> och hur de påverkar olika livsområden i ditt horoskop.", "titleLength": 55, "descriptionLength": 119}, {"url": "/astrologi-lara/husen/nionde-huset", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\husen\\nionde-huset\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/astrologi-lara/husen/sjatte-huset", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\husen\\sjatte-huset\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/astrologi-lara/husen/sjunde-huset", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\husen\\sjunde-huset\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/astrologi-lara/husen/tionde-huset", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\husen\\tionde-huset\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/astrologi-lara/husen/tolfte-huset", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\husen\\tolfte-huset\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/astrologi-lara/husen/tomma-hus", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\husen\\tomma-hus\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/astrologi-lara/husen/tredje-huset", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\husen\\tredje-huset\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/astrologi-lara/planeter", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\planeter\\page.tsx", "hasMetadata": true, "hasTitle": true, "hasDescription": true, "hasCanonical": true, "isDynamic": false, "title": "Planeterna i astrologi - Energier och betydelser | Horoskopet.nu", "description": "<PERSON><PERSON><PERSON> dig om planeternas betydelse i astrologin. <PERSON><PERSON><PERSON><PERSON> <PERSON>ur <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Venus, Mars, Jupiter, Saturnus, Uranus, Neptunus och Pluto p<PERSON>kar ditt liv.", "titleLength": 64, "descriptionLength": 161}, {"url": "/astrologi-lara/planeter/jupiter", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\planeter\\jupiter\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/astrologi-lara/planeter/manen", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\planeter\\manen\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/astrologi-lara/planeter/mars", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\planeter\\mars\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/astrologi-lara/planeter/merkurius", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\planeter\\merkurius\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/astrologi-lara/planeter/merkurius-retrograd", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\planeter\\merkurius-retrograd\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/astrologi-lara/planeter/neptunus", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\planeter\\neptunus\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/astrologi-lara/planeter/pluto", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\planeter\\pluto\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/astrologi-lara/planeter/saturnus", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\planeter\\saturnus\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/astrologi-lara/planeter/solen", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\planeter\\solen\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/astrologi-lara/planeter/uranus", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\planeter\\uranus\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/astrologi-lara/planeter/venus", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\planeter\\venus\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/astrologi-lara/synastri", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\synastri\\page.tsx", "hasMetadata": true, "hasTitle": true, "hasDescription": true, "hasCanonical": false, "isDynamic": false, "title": "Synastri - Astrologisk kompatibilitet | Horoskopet.nu", "description": "<PERSON><PERSON>r dig om synastri, den astrologiska metoden för att analysera kompatibilitet mellan två personers födelsehoroskop.", "titleLength": 53, "descriptionLength": 116}, {"url": "/astrologi-lara/zodiak", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\zodiak\\page.tsx", "hasMetadata": true, "hasTitle": true, "hasDescription": true, "hasCanonical": false, "isDynamic": false, "title": "Zodiak och stjärntecken | Horoskopet.nu", "description": "<PERSON><PERSON><PERSON> dig om zodiaken, de tolv stjärntecknen och deras egenska<PERSON>, symboler och betydelser i astrologi.", "titleLength": 39, "descriptionLength": 101}, {"url": "/astrologi-lara/zodiak/angransande-tecken", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\zodiak\\angransande-tecken\\page.tsx", "hasMetadata": true, "hasTitle": true, "hasDescription": true, "hasCanonical": false, "isDynamic": false, "title": "Angränsande stjärntecken | Horoskopet.nu", "description": "<PERSON><PERSON>r dig om relationerna mellan intilliggande stjärntecken i zodiaken och hur de påverkar varandra astrologiskt.", "titleLength": 40, "descriptionLength": 111}, {"url": "/cookies", "filePath": "C:\\Dev\\horoskopetnew\\app\\cookies\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/horoskop", "filePath": "C:\\Dev\\horoskopetnew\\app\\horoskop\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/horoskop/arkiv", "filePath": "C:\\Dev\\horoskopetnew\\app\\horoskop\\arkiv\\page.tsx", "hasMetadata": false, "hasTitle": true, "hasDescription": true, "hasCanonical": false, "isDynamic": false, "title": "Månadens Horoskop December 2023", "description": "Detaljerade förutsägelser för alla stjärntecken under december 2023", "titleLength": 31, "descriptionLength": 67}, {"url": "/horoskop/arshoroskop", "filePath": "C:\\Dev\\horoskopetnew\\app\\horoskop\\arshoroskop\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/horoskop/dagens", "filePath": "C:\\Dev\\horoskopetnew\\app\\horoskop\\dagens\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/horoskop/dagens/[sign]", "filePath": "C:\\Dev\\horoskopetnew\\app\\horoskop\\dagens\\[sign]\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/horoskop/manadens", "filePath": "C:\\Dev\\horoskopetnew\\app\\horoskop\\manadens\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/horoskop/manadens/[sign]", "filePath": "C:\\Dev\\horoskopetnew\\app\\horoskop\\manadens\\[sign]\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/horoskop/special", "filePath": "C:\\Dev\\horoskopetnew\\app\\horoskop\\special\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/horoskop/veckans", "filePath": "C:\\Dev\\horoskopetnew\\app\\horoskop\\veckans\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/horoskop/veckans/[sign]", "filePath": "C:\\Dev\\horoskopetnew\\app\\horoskop\\veckans\\[sign]\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/integritetspolicy", "filePath": "C:\\Dev\\horoskopetnew\\app\\integritetspolicy\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/kontakt", "filePath": "C:\\Dev\\horoskopetnew\\app\\kontakt\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/nyhetsbrev", "filePath": "C:\\Dev\\horoskopetnew\\app\\nyhetsbrev\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/om-oss", "filePath": "C:\\Dev\\horoskopetnew\\app\\om-oss\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/relationer", "filePath": "C:\\Dev\\horoskopetnew\\app\\relationer\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/relationer/fiskarna", "filePath": "C:\\Dev\\horoskopetnew\\app\\relationer\\fiskarna\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/relationer/jungfrun", "filePath": "C:\\Dev\\horoskopetnew\\app\\relationer\\jungfrun\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/relationer/kraftan", "filePath": "C:\\Dev\\horoskopetnew\\app\\relationer\\kraftan\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/relationer/lejonet", "filePath": "C:\\Dev\\horoskopetnew\\app\\relationer\\lejonet\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/relationer/matchning", "filePath": "C:\\Dev\\horoskopetnew\\app\\relationer\\matchning\\page.tsx", "hasMetadata": true, "hasTitle": true, "hasDescription": true, "hasCanonical": false, "isDynamic": false, "title": "Stjärnteckensmatchning | Horoskopet.nu", "description": "Upptäck vilka stjärntecken som passar bäst tillsammans med vår interaktiva kompatibilitetsguide, matris och kalkylator.", "titleLength": 38, "descriptionLength": 119}, {"url": "/relationer/oxen", "filePath": "C:\\Dev\\horoskopetnew\\app\\relationer\\oxen\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/relationer/skorpionen", "filePath": "C:\\Dev\\horoskopetnew\\app\\relationer\\skorpionen\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/relationer/skytten", "filePath": "C:\\Dev\\horoskopetnew\\app\\relationer\\skytten\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/relationer/stenbocken", "filePath": "C:\\Dev\\horoskopetnew\\app\\relationer\\stenbocken\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/relationer/tvillingarna", "filePath": "C:\\Dev\\horoskopetnew\\app\\relationer\\tvillingarna\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/relationer/vaduren", "filePath": "C:\\Dev\\horoskopetnew\\app\\relationer\\vaduren\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/relationer/vagen", "filePath": "C:\\Dev\\horoskopetnew\\app\\relationer\\vagen\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/relationer/vattumannen", "filePath": "C:\\Dev\\horoskopetnew\\app\\relationer\\vattumannen\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/relationer/[sign]", "filePath": "C:\\Dev\\horoskopetnew\\app\\relationer\\[sign]\\page.tsx", "hasMetadata": true, "hasTitle": true, "hasDescription": true, "hasCanonical": false, "isDynamic": true, "title": "${sign?.name || ", "description": "<PERSON><PERSON><PERSON> dig om ${sign?.name ? sign.name.toLowerCase() : ", "titleLength": 16, "descriptionLength": 52}, {"url": "/relationer/[sign]/[sign2]", "filePath": "C:\\Dev\\horoskopetnew\\app\\relationer\\[sign]\\[sign2]\\page.tsx", "hasMetadata": true, "hasTitle": true, "hasDescription": true, "hasCanonical": false, "isDynamic": true, "title": "Stjärntecken hittades inte | Horoskopet.nu", "description": "Vi kunde inte hitta information om det angivna stjärntecknet.", "titleLength": 42, "descriptionLength": 61}, {"url": "/sitemap", "filePath": "C:\\Dev\\horoskopetnew\\app\\sitemap\\page.tsx", "hasMetadata": false, "hasTitle": true, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": "<PERSON><PERSON>", "description": null, "titleLength": 3, "descriptionLength": 0}, {"url": "/stjar<PERSON>cken", "filePath": "C:\\Dev\\horoskopetnew\\app\\stjarntecken\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": true, "hasCanonical": false, "isDynamic": false, "title": null, "description": "Väduren är det första tecknet i zodiaken och kännetecknas av energi, mod och ledarskap. Väduren är en initiativtagare som älskar utmaningar och nya äventyr.", "titleLength": 0, "descriptionLength": 156}, {"url": "/stjarntecken/[sign]", "filePath": "C:\\Dev\\horoskopetnew\\app\\stjarntecken\\[sign]\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}], "stats": {"total": 108, "hasMetadata": 28, "hasTitle": 30, "hasDescription": 32, "hasCanonical": 14, "isDynamic": 2, "titleTooShort": 2, "titleTooLong": 4, "descTooShort": 14, "descTooLong": 10}, "problems": {"noMetadata": [{"url": "/astrologi-lara/aspekter/biquintil", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\aspekter\\biquintil\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/astrologi-lara/aspekter/halvkvadrat", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\aspekter\\halvkvadrat\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/astrologi-lara/aspekter/kvinkuns", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\aspekter\\kvinkuns\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/astrologi-lara/aspekter/kvintil", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\aspekter\\kvintil\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/astrologi-lara/aspekter/monster", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\aspekter\\monster\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/astrologi-lara/aspekter/monster/grand-cross", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\aspekter\\monster\\grand-cross\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/astrologi-lara/aspekter/monster/gyllene-yod", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\aspekter\\monster\\gyllene-yod\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/astrologi-lara/aspekter/monster/kite", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\aspekter\\monster\\kite\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/astrologi-lara/aspekter/monster/mystiska-rektangeln", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\aspekter\\monster\\mystiska-rektangeln\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/astrologi-lara/aspekter/monster/stortrigon", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\aspekter\\monster\\stortrigon\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/astrologi-lara/aspekter/monster/t-kvadratur", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\aspekter\\monster\\t-kvadratur\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/astrologi-lara/aspekter/monster/thors-hammare", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\aspekter\\monster\\thors-hammare\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/astrologi-lara/aspekter/monster/visualisering", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\aspekter\\monster\\visualisering\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/astrologi-lara/aspekter/monster/yod", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\aspekter\\monster\\yod\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/astrologi-lara/aspekter/semisextil", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\aspekter\\semisextil\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/astrologi-lara/aspekter/seskvikvadratur", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\aspekter\\seskvikvadratur\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/astrologi-lara/grunderna/element-och-kvaliteter", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\grunderna\\element-och-kvaliteter\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": true, "hasCanonical": false, "isDynamic": false, "title": null, "description": "Eldelement representerar energi, passion och kreativitet. Personer med starka eldelement i sitt horoskop tenderar att vara entusiastiska, modiga och självständiga.", "titleLength": 0, "descriptionLength": 163}, {"url": "/astrologi-lara/grunderna/fodelsehoroskop", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\grunderna\\fodelsehoroskop\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": true, "hasCanonical": false, "isDynamic": false, "title": null, "description": "Representerar din personlighet, fysiska kropp och hur du presenterar dig för världen. Det är tecknet som steg upp över horisonten vid din födelse.", "titleLength": 0, "descriptionLength": 146}, {"url": "/astrologi-lara/grunderna/vad-ar-astrologi", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\grunderna\\vad-ar-astrologi\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/astrologi-lara/grunderna/zodiakens-symbolik", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\grunderna\\zodiakens-symbolik\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/astrologi-lara/husen/andra-huset", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\husen\\andra-huset\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/astrologi-lara/husen/angulara-hus", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\husen\\angulara-hus\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/astrologi-lara/husen/attonde-huset", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\husen\\attonde-huset\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/astrologi-lara/husen/derivativa-hus", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\husen\\derivativa-hus\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/astrologi-lara/husen/elfte-huset", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\husen\\elfte-huset\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/astrologi-lara/husen/femte-huset", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\husen\\femte-huset\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/astrologi-lara/husen/fjarde-huset", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\husen\\fjarde-huset\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/astrologi-lara/husen/forsta-huset", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\husen\\forsta-huset\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/astrologi-lara/husen/husspetsar", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\husen\\husspetsar\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/astrologi-lara/husen/hussystem", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\husen\\hussystem\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/astrologi-lara/husen/intercepterade-hus", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\husen\\intercepterade-hus\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/astrologi-lara/husen/nionde-huset", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\husen\\nionde-huset\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/astrologi-lara/husen/sjatte-huset", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\husen\\sjatte-huset\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/astrologi-lara/husen/sjunde-huset", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\husen\\sjunde-huset\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/astrologi-lara/husen/tionde-huset", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\husen\\tionde-huset\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/astrologi-lara/husen/tolfte-huset", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\husen\\tolfte-huset\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/astrologi-lara/husen/tomma-hus", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\husen\\tomma-hus\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/astrologi-lara/husen/tredje-huset", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\husen\\tredje-huset\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/astrologi-lara/planeter/jupiter", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\planeter\\jupiter\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/astrologi-lara/planeter/manen", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\planeter\\manen\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/astrologi-lara/planeter/mars", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\planeter\\mars\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/astrologi-lara/planeter/merkurius", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\planeter\\merkurius\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/astrologi-lara/planeter/merkurius-retrograd", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\planeter\\merkurius-retrograd\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/astrologi-lara/planeter/neptunus", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\planeter\\neptunus\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/astrologi-lara/planeter/pluto", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\planeter\\pluto\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/astrologi-lara/planeter/saturnus", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\planeter\\saturnus\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/astrologi-lara/planeter/solen", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\planeter\\solen\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/astrologi-lara/planeter/uranus", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\planeter\\uranus\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/astrologi-lara/planeter/venus", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\planeter\\venus\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/cookies", "filePath": "C:\\Dev\\horoskopetnew\\app\\cookies\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/horoskop", "filePath": "C:\\Dev\\horoskopetnew\\app\\horoskop\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/horoskop/arkiv", "filePath": "C:\\Dev\\horoskopetnew\\app\\horoskop\\arkiv\\page.tsx", "hasMetadata": false, "hasTitle": true, "hasDescription": true, "hasCanonical": false, "isDynamic": false, "title": "Månadens Horoskop December 2023", "description": "Detaljerade förutsägelser för alla stjärntecken under december 2023", "titleLength": 31, "descriptionLength": 67}, {"url": "/horoskop/arshoroskop", "filePath": "C:\\Dev\\horoskopetnew\\app\\horoskop\\arshoroskop\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/horoskop/dagens", "filePath": "C:\\Dev\\horoskopetnew\\app\\horoskop\\dagens\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/horoskop/dagens/[sign]", "filePath": "C:\\Dev\\horoskopetnew\\app\\horoskop\\dagens\\[sign]\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/horoskop/manadens", "filePath": "C:\\Dev\\horoskopetnew\\app\\horoskop\\manadens\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/horoskop/manadens/[sign]", "filePath": "C:\\Dev\\horoskopetnew\\app\\horoskop\\manadens\\[sign]\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/horoskop/special", "filePath": "C:\\Dev\\horoskopetnew\\app\\horoskop\\special\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/horoskop/veckans", "filePath": "C:\\Dev\\horoskopetnew\\app\\horoskop\\veckans\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/horoskop/veckans/[sign]", "filePath": "C:\\Dev\\horoskopetnew\\app\\horoskop\\veckans\\[sign]\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/integritetspolicy", "filePath": "C:\\Dev\\horoskopetnew\\app\\integritetspolicy\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/kontakt", "filePath": "C:\\Dev\\horoskopetnew\\app\\kontakt\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/nyhetsbrev", "filePath": "C:\\Dev\\horoskopetnew\\app\\nyhetsbrev\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/om-oss", "filePath": "C:\\Dev\\horoskopetnew\\app\\om-oss\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/relationer", "filePath": "C:\\Dev\\horoskopetnew\\app\\relationer\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/relationer/fiskarna", "filePath": "C:\\Dev\\horoskopetnew\\app\\relationer\\fiskarna\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/relationer/jungfrun", "filePath": "C:\\Dev\\horoskopetnew\\app\\relationer\\jungfrun\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/relationer/kraftan", "filePath": "C:\\Dev\\horoskopetnew\\app\\relationer\\kraftan\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/relationer/lejonet", "filePath": "C:\\Dev\\horoskopetnew\\app\\relationer\\lejonet\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/relationer/oxen", "filePath": "C:\\Dev\\horoskopetnew\\app\\relationer\\oxen\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/relationer/skorpionen", "filePath": "C:\\Dev\\horoskopetnew\\app\\relationer\\skorpionen\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/relationer/skytten", "filePath": "C:\\Dev\\horoskopetnew\\app\\relationer\\skytten\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/relationer/stenbocken", "filePath": "C:\\Dev\\horoskopetnew\\app\\relationer\\stenbocken\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/relationer/tvillingarna", "filePath": "C:\\Dev\\horoskopetnew\\app\\relationer\\tvillingarna\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/relationer/vaduren", "filePath": "C:\\Dev\\horoskopetnew\\app\\relationer\\vaduren\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/relationer/vagen", "filePath": "C:\\Dev\\horoskopetnew\\app\\relationer\\vagen\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/relationer/vattumannen", "filePath": "C:\\Dev\\horoskopetnew\\app\\relationer\\vattumannen\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}, {"url": "/sitemap", "filePath": "C:\\Dev\\horoskopetnew\\app\\sitemap\\page.tsx", "hasMetadata": false, "hasTitle": true, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": "<PERSON><PERSON>", "description": null, "titleLength": 3, "descriptionLength": 0}, {"url": "/stjar<PERSON>cken", "filePath": "C:\\Dev\\horoskopetnew\\app\\stjarntecken\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": true, "hasCanonical": false, "isDynamic": false, "title": null, "description": "Väduren är det första tecknet i zodiaken och kännetecknas av energi, mod och ledarskap. Väduren är en initiativtagare som älskar utmaningar och nya äventyr.", "titleLength": 0, "descriptionLength": 156}, {"url": "/stjarntecken/[sign]", "filePath": "C:\\Dev\\horoskopetnew\\app\\stjarntecken\\[sign]\\page.tsx", "hasMetadata": false, "hasTitle": false, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": null, "description": null, "titleLength": 0, "descriptionLength": 0}], "noTitle": [], "noDescription": [], "titleProblems": [{"url": "/astrologi-lara/aspekter", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\aspekter\\page.tsx", "hasMetadata": true, "hasTitle": true, "hasDescription": true, "hasCanonical": true, "isDynamic": false, "title": "Astrologiska aspekter - Planeternas relationer | Horoskopet.nu", "description": "<PERSON><PERSON>r dig om astrologiska aspekter - vinklarna mellan planeter som skapar energimönster i ditt horoskop. Upptäck konjunktioner, trigoner, k<PERSON>draturer och mer.", "titleLength": 62, "descriptionLength": 156}, {"url": "/astrologi-lara/grunderna", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\grunderna\\page.tsx", "hasMetadata": true, "hasTitle": true, "hasDescription": true, "hasCanonical": true, "isDynamic": false, "title": "Grunderna i astrologi - Element, kvaliteter och mer | Horoskopet.nu", "description": "Lär dig astrologins grundläggande principer. Upptäck element, kvaliteter, zodiakens symbolik och hur födelsehoroskop skapas. Perfekt för nybörjare.", "titleLength": 67, "descriptionLength": 147}, {"url": "/astrologi-lara/husen", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\husen\\page.tsx", "hasMetadata": true, "hasTitle": true, "hasDescription": true, "hasCanonical": true, "isDynamic": false, "title": "De 12 astrologiska husen - Livsområden i horoskopet | Horoskopet.nu", "description": "<PERSON><PERSON>r dig om de 12 astrologiska husen som representerar olika livsområden. Upptäck hur husen visar var planeternas energier kommer till uttryck i ditt liv.", "titleLength": 67, "descriptionLength": 153}, {"url": "/astrologi-lara/planeter", "filePath": "C:\\Dev\\horoskopetnew\\app\\astrologi-lara\\planeter\\page.tsx", "hasMetadata": true, "hasTitle": true, "hasDescription": true, "hasCanonical": true, "isDynamic": false, "title": "Planeterna i astrologi - Energier och betydelser | Horoskopet.nu", "description": "<PERSON><PERSON><PERSON> dig om planeternas betydelse i astrologin. <PERSON><PERSON><PERSON><PERSON> <PERSON>ur <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Venus, Mars, Jupiter, Saturnus, Uranus, Neptunus och Pluto p<PERSON>kar ditt liv.", "titleLength": 64, "descriptionLength": 161}, {"url": "/relationer/[sign]", "filePath": "C:\\Dev\\horoskopetnew\\app\\relationer\\[sign]\\page.tsx", "hasMetadata": true, "hasTitle": true, "hasDescription": true, "hasCanonical": false, "isDynamic": true, "title": "${sign?.name || ", "description": "<PERSON><PERSON><PERSON> dig om ${sign?.name ? sign.name.toLowerCase() : ", "titleLength": 16, "descriptionLength": 52}, {"url": "/sitemap", "filePath": "C:\\Dev\\horoskopetnew\\app\\sitemap\\page.tsx", "hasMetadata": false, "hasTitle": true, "hasDescription": false, "hasCanonical": false, "isDynamic": false, "title": "<PERSON><PERSON>", "description": null, "titleLength": 3, "descriptionLength": 0}]}}