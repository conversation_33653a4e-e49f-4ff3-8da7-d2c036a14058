# Horoskopet.nu Design System

## Översikt
Detta dokument beskriver designsystemet för Horoskopet.nu baserat på första sidans design som ska återanvändas konsekvent på alla undersidor.

## Färgschema

### Prim<PERSON>ra färger
- **Cosmic Purple**: `#6e56cf` - Huvudfärg för knappar och accenter
- **Light Purple**: `#a78bfa` - Ljusare variant för hover-effekter och ikoner
- **Dark Base**: `#0c0817` - Mörkaste bakgrundsfärg
- **Medium Dark**: `#1a1333` - <PERSON><PERSON><PERSON> för kort och sektioner
- **Darker Purple**: `#221a44` - Gradient mellanfärg
- **Deep Purple**: `#2d1d57` - Djupaste gradient-färg

### Textfärger
- **Vit text**: `text-white` - Huvudtext, rubriker och viktiga begrepp (strong-taggar)
- **L<PERSON><PERSON> gr<PERSON>**: `text-slate-300` - Sekundär text, beskrivningar och förklarande text
- **Cosmic text**: `text-cosmic-purple-600` - Accent text

#### Textfärgsregler för listor:
- **Listcontainer**: Ingen textfärg på `<ul>` - låt varje element ha sin egen färg
- **Huvudbegrepp**: `<strong className="text-white">` för viktiga termer
- **Beskrivningar**: `<span className="text-slate-300">` för förklarande text
- **Paragrafer**: `text-white` för huvudtext, `text-slate-300` för sekundär information

## Bakgrunder

### Huvudbakgrund (Body)
```css
background: linear-gradient(135deg, #1a1333 0%, #221a44 40%, #2d1d57 100%);
background-attachment: fixed;
```

### Stjärnbakgrund
- **Fixed overlay för hela sidan** (ANVÄND DENNA - inte cosmic-bg!):
```html
<div className="fixed inset-0 -z-10 overflow-hidden pointer-events-none">
  <div className="absolute inset-0 bg-[#0c0817] opacity-90"></div>
  <div className="stars-small opacity-30"></div>
</div>
```

**VIKTIGT**: Använd INTE `cosmic-bg` eller andra overlays som skapar mörka effekter över innehållet!

### Kort/Card bakgrunder
- **Primär**: `bg-[#1a1333]/80` med `border border-[#6e56cf]/30`
- **Hover**: `hover:border-[#6e56cf]/40`
- **Shadow**: `shadow-lg shadow-primary/10`

## Typografi

### Fonter
- **Display font**: Cinzel (rubriker) - `font-display`
- **Body font**: Inter (brödtext) - `font-sans`
- **Accent font**: Playfair Display - `font-playfair`

### Rubrikstorlekar
- **H1**: `text-4xl md:text-5xl lg:text-6xl` med `cosmic-title` klass
- **H2**: `text-2xl md:text-3xl`
- **H3**: `text-xl md:text-2xl`

### Specialklasser
- **cosmic-title**: Gradient text-effekt för huvudrubriker
- **cosmic-gradient-text**: Alternativ gradient text

## Knappar

### Primär knapp
```html
<a className="inline-block py-3 px-8 bg-gradient-to-r from-[#6e56cf]/80 to-[#a78bfa]/80 hover:from-[#6e56cf]/90 hover:to-[#a78bfa]/90 text-white font-medium rounded-md border border-[#a78bfa]/30 shadow-md hover:shadow-lg transition-all duration-300">
```

### Sekundär knapp
```html
<a className="inline-block py-3 px-8 bg-[#6e56cf]/20 hover:bg-[#6e56cf]/30 text-white font-medium rounded-md border border-[#6e56cf]/30 shadow-md hover:shadow-lg transition-all duration-300">
```

## Layout-struktur

### Container
- **Standard**: `container mx-auto px-4`
- **Max width**: `max-w-4xl mx-auto` för innehåll
- **Spacing**: `space-y-16 pb-16` för sektioner

### Hero-sektion mall
```html
<section className="relative min-h-[80vh] flex items-center justify-center overflow-hidden">
  <!-- Stjärnbakgrund -->
  <div className="fixed inset-0 -z-10 overflow-hidden pointer-events-none">
    <div className="absolute inset-0 bg-[#0c0817] opacity-90"></div>
    <div className="stars-small opacity-30"></div>
  </div>

  <!-- Innehåll -->
  <div className="relative z-10 container mx-auto px-4 text-center">
    <div className="max-w-4xl mx-auto">
      <h1 className="text-4xl md:text-5xl lg:text-6xl font-display tracking-tight mb-6 cosmic-title leading-tight">
        Rubrik här
      </h1>
      <p className="text-lg md:text-xl text-slate-300 mb-8 max-w-2xl mx-auto drop-shadow-md">
        Beskrivning här
      </p>
    </div>
  </div>
</section>
```

## Kort/Cards

### Standard kort
```html
<Card className="overflow-hidden border border-[#6e56cf]/30 bg-[#1a1333]/80 hover:border-[#6e56cf]/40 transition-all duration-300 shadow-lg shadow-primary/10">
```

## Animationer och effekter

### CSS-klasser
- **stars-small**: Animerad stjärnbakgrund
- **cosmic-title**: Gradient text-effekt
- **animate-twinkle**: Blinkande stjärneffekt
- **transition-all duration-300**: Standard övergångar

### Hover-effekter
- **Skalning**: `group-hover:scale-110 transition-transform`
- **Färgövergångar**: `hover:text-[#a78bfa] transition-colors duration-300`

## Ikoner
- **Primära ikoner**: Star, Sparkles, Moon, Sun från Lucide React
- **Färg**: `text-[#a78bfa]` för accent-ikoner
- **Storlek**: `h-5 w-5` eller `h-6 w-6` beroende på kontext

## Navigation och Länkar

### Stjärnteckensnavigation
**KRITISKT**: Använd ALLTID `sign.path` för navigation, INTE `sign.slug`:

```jsx
// ✅ KORREKT
{zodiacData.map((sign) => (
  <Link
    key={sign.path}
    href={`/relationer/${sign.path}`}
    className={sign.path === zodiacSign.path ? "active" : ""}
  >
    {sign.name}
  </Link>
))}

// ❌ FEL - skapar undefined länkar
href={`/relationer/${sign.slug}`}  // sign.slug finns inte!
```

**Anledning**: `zodiacData` har endast `path` property, inte `slug`.

## Spacing och padding

### Hero-sektioner
- **Hero-höjd**: `min-h-[60vh]` för optimala proportioner
- **Container**: `py-8` för balanserat avstånd till innehåll
- **Sektionsavstånd**: `space-y-12` för tät men luftig layout

### Allmän spacing
- **Stora sektioner**: `py-12` eller `py-16`
- **Kort**: `p-4` eller `p-6`
- **Knappar**: `py-3 px-8`
- **Mellanrum**: `space-y-8` eller `space-y-12` för vertikal spacing
- **Marginaler**: `mb-8` eller `mb-12` mellan huvudsektioner

## Responsivitet
- **Breakpoints**: sm, md, lg, xl enligt Tailwind standard
- **Grid**: `grid-cols-1 md:grid-cols-2 lg:grid-cols-3`
- **Text**: Responsiva textstorlekar med md: och lg: prefixer

## Header och Footer
- **Header**: Fixed position med backdrop-blur och stjärnbakgrund
- **Footer**: Gradient bakgrund med stjärndetaljer och strukturerad navigation

## Implementeringsriktlinjer
1. Använd alltid stjärnbakgrund på huvudsektioner
2. Behåll konsekvent färgschema med cosmic purple-teman
3. Använd gradient-knappar för primära actions
4. Implementera smooth transitions på alla interaktiva element
5. Säkerställ responsiv design på alla komponenter
