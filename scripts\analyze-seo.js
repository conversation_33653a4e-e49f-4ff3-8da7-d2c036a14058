const fs = require('fs')
const path = require('path')

// <PERSON><PERSON> för att extrahera metadata från en page.tsx fil
function extractMetadata(filePath) {
  try {
    if (!fs.existsSync(filePath)) {
      return null
    }
    
    const content = fs.readFileSync(filePath, 'utf8')
    
    // Kolla om det är en redirect-sida
    if (content.includes('redirect(')) {
      return { type: 'redirect' }
    }
    
    const metadata = {
      hasMetadata: false,
      hasTitle: false,
      hasDescription: false,
      hasCanonical: false,
      isDynamic: false,
      title: null,
      description: null,
      titleLength: 0,
      descriptionLength: 0
    }
    
    // Kolla om det finns metadata export
    if (content.includes('export const metadata') || content.includes('export async function generateMetadata')) {
      metadata.hasMetadata = true
      metadata.isDynamic = content.includes('generateMetadata')
    }
    
    // Extrahera title
    const titleMatch = content.match(/title:\s*["`']([^"`']+)["`']/s) || 
                      content.match(/title:\s*`([^`]+)`/s)
    if (titleMatch) {
      metadata.hasTitle = true
      metadata.title = titleMatch[1].replace(/\$\{[^}]+\}/g, '[DYNAMIC]')
      metadata.titleLength = titleMatch[1].length
    }
    
    // Extrahera description
    const descMatch = content.match(/description:\s*["`']([^"`']+)["`']/s) || 
                      content.match(/description:\s*`([^`]+)`/s)
    if (descMatch) {
      metadata.hasDescription = true
      metadata.description = descMatch[1].replace(/\$\{[^}]+\}/g, '[DYNAMIC]')
      metadata.descriptionLength = descMatch[1].length
    }
    
    // Kolla canonical (kommer implementeras senare)
    metadata.hasCanonical = content.includes('canonical')
    
    return metadata
    
  } catch (error) {
    console.error(`Error reading file ${filePath}:`, error)
    return null
  }
}

// Funktion för att hitta alla page.tsx filer och analysera dem
function analyzeAllPages(dir, basePath = '') {
  const results = []
  
  try {
    const items = fs.readdirSync(dir, { withFileTypes: true })
    
    for (const item of items) {
      const fullPath = path.join(dir, item.name)
      const urlPath = path.join(basePath, item.name).replace(/\\/g, '/')
      
      if (item.isDirectory()) {
        const pagePath = path.join(fullPath, 'page.tsx')
        if (fs.existsSync(pagePath)) {
          const metadata = extractMetadata(pagePath)
          if (metadata) {
            results.push({
              url: urlPath === '' ? '/' : `/${urlPath}`,
              filePath: pagePath,
              ...metadata
            })
          }
        }
        
        results.push(...analyzeAllPages(fullPath, urlPath))
      }
    }
  } catch (error) {
    console.error(`Error reading directory ${dir}:`, error)
  }
  
  return results
}

// Huvudfunktion för SEO-analys
function analyzeSEO() {
  console.log('🔍 Analyserar SEO metadata för alla sidor...\n')
  
  const appDir = path.join(process.cwd(), 'app')
  const pages = analyzeAllPages(appDir)
  
  // Filtrera bort redirect-sidor
  const validPages = pages.filter(page => page.type !== 'redirect')
  const redirectPages = pages.filter(page => page.type === 'redirect')
  
  console.log(`📊 Analys av ${validPages.length} sidor (${redirectPages.length} redirect-sidor exkluderade)\n`)
  
  // Statistik
  const stats = {
    total: validPages.length,
    hasMetadata: validPages.filter(p => p.hasMetadata).length,
    hasTitle: validPages.filter(p => p.hasTitle).length,
    hasDescription: validPages.filter(p => p.hasDescription).length,
    hasCanonical: validPages.filter(p => p.hasCanonical).length,
    isDynamic: validPages.filter(p => p.isDynamic).length,
    titleTooShort: validPages.filter(p => p.titleLength > 0 && p.titleLength < 30).length,
    titleTooLong: validPages.filter(p => p.titleLength > 60).length,
    descTooShort: validPages.filter(p => p.descriptionLength > 0 && p.descriptionLength < 120).length,
    descTooLong: validPages.filter(p => p.descriptionLength > 160).length
  }
  
  console.log('📈 STATISTIK:')
  console.log(`✅ Har metadata: ${stats.hasMetadata}/${stats.total} (${Math.round(stats.hasMetadata/stats.total*100)}%)`)
  console.log(`📝 Har title: ${stats.hasTitle}/${stats.total} (${Math.round(stats.hasTitle/stats.total*100)}%)`)
  console.log(`📄 Har description: ${stats.hasDescription}/${stats.total} (${Math.round(stats.hasDescription/stats.total*100)}%)`)
  console.log(`🔗 Har canonical: ${stats.hasCanonical}/${stats.total} (${Math.round(stats.hasCanonical/stats.total*100)}%)`)
  console.log(`⚡ Dynamiska sidor: ${stats.isDynamic}/${stats.total} (${Math.round(stats.isDynamic/stats.total*100)}%)`)
  console.log('')
  
  console.log('⚠️  PROBLEM:')
  console.log(`📏 Title för kort (<30 tecken): ${stats.titleTooShort}`)
  console.log(`📏 Title för lång (>60 tecken): ${stats.titleTooLong}`)
  console.log(`📄 Description för kort (<120 tecken): ${stats.descTooShort}`)
  console.log(`📄 Description för lång (>160 tecken): ${stats.descTooLong}`)
  console.log('')
  
  // Sidor utan metadata
  const noMetadata = validPages.filter(p => !p.hasMetadata)
  if (noMetadata.length > 0) {
    console.log('❌ SIDOR UTAN METADATA:')
    noMetadata.forEach(page => {
      console.log(`   ${page.url}`)
    })
    console.log('')
  }
  
  // Sidor utan title
  const noTitle = validPages.filter(p => p.hasMetadata && !p.hasTitle)
  if (noTitle.length > 0) {
    console.log('❌ SIDOR UTAN TITLE:')
    noTitle.forEach(page => {
      console.log(`   ${page.url}`)
    })
    console.log('')
  }
  
  // Sidor utan description
  const noDescription = validPages.filter(p => p.hasMetadata && !p.hasDescription)
  if (noDescription.length > 0) {
    console.log('❌ SIDOR UTAN DESCRIPTION:')
    noDescription.forEach(page => {
      console.log(`   ${page.url}`)
    })
    console.log('')
  }
  
  // Title-problem
  const titleProblems = validPages.filter(p => p.hasTitle && (p.titleLength < 30 || p.titleLength > 60))
  if (titleProblems.length > 0) {
    console.log('⚠️  TITLE-LÄNGD PROBLEM:')
    titleProblems.forEach(page => {
      const status = page.titleLength < 30 ? 'FÖR KORT' : 'FÖR LÅNG'
      console.log(`   ${page.url} (${page.titleLength} tecken - ${status})`)
      console.log(`      "${page.title}"`)
    })
    console.log('')
  }
  
  return {
    pages: validPages,
    stats,
    problems: {
      noMetadata,
      noTitle,
      noDescription,
      titleProblems
    }
  }
}

// Kör analysen
const results = analyzeSEO()

// Spara resultat till fil för vidare analys
const outputPath = path.join(process.cwd(), 'seo-analysis.json')
fs.writeFileSync(outputPath, JSON.stringify(results, null, 2))
console.log(`💾 Detaljerad analys sparad till: ${outputPath}`)
