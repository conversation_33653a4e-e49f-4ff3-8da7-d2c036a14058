const fs = require('fs');
const path = require('path');

// All zodiac signs with their paths
const zodiacSigns = [
  'vaduren', 'oxen', 'tvillingarna', 'kraftan', 'lejonet', 'jungfrun',
  'vagen', 'skorpionen', 'sky<PERSON>', 'stenbocken', 'vattuman<PERSON>', 'fiskarna'
];

// Function to update compatibility links in a zodiac sign page
function updateCompatibilityLinks(signPath) {
  const filePath = path.join(__dirname, '..', 'app', 'relationer', signPath, 'page.tsx');
  
  if (!fs.existsSync(filePath)) {
    console.log(`File not found: ${filePath}`);
    return;
  }
  
  let content = fs.readFileSync(filePath, 'utf8');
  
  // Add import for CompatibilityLink and ZodiacNavigationLink if not present
  if (!content.includes('CompatibilityLink')) {
    content = content.replace(
      /import Link from "next\/link"/,
      `import { CompatibilityLink, ZodiacNavigationLink } from "@/components/compatibility/compatibility-link"\nimport Link from "next/link"`
    );
  }
  
  // Replace compatibility links in matchning sections
  // Pattern: <Link href="/relationer/[sign]" className="text-[#a78bfa] hover:text-[#c4b5fd] font-semibold transition-colors">
  const linkPattern = /<Link href="\/relationer\/([^"]+)" className="text-\[#a78bfa\] hover:text-\[#c4b5fd\] font-semibold transition-colors">/g;
  
  content = content.replace(linkPattern, (match, targetSign) => {
    return `<CompatibilityLink currentSign="${signPath}" targetSign="${targetSign}" className="text-[#a78bfa] hover:text-[#c4b5fd] font-semibold transition-colors">`;
  });
  
  // Replace closing Link tags with CompatibilityLink
  content = content.replace(/<\/Link>/g, '</CompatibilityLink>');
  
  // Replace navigation links in "Utforska" section
  const navigationPattern = /href={\`\/relationer\/\$\{sign\.path\}\`}/g;
  content = content.replace(navigationPattern, `href={\`/relationer/${signPath}/\${sign.path}\`}`);
  
  // Replace Link component with ZodiacNavigationLink in navigation section
  const navLinkPattern = /<Link\s+key=\{sign\.path\}\s+href=\{[^}]+\}\s+className=\{[^}]+\}>/g;
  content = content.replace(navLinkPattern, (match) => {
    return match.replace('<Link', '<ZodiacNavigationLink')
      .replace('href={`/relationer/${signPath}/${sign.path}`}', '')
      + `\n                  currentSign="${signPath}"\n                  targetSign={sign.path}\n                  targetSignName={sign.name}\n                  targetSignPath={sign.path}\n                  isCurrentSign={sign.path === zodiacSign.path}`;
  });
  
  // Replace closing Link tags in navigation with ZodiacNavigationLink
  const navClosingPattern = /<\/Link>/g;
  content = content.replace(navClosingPattern, '</ZodiacNavigationLink>');
  
  // Write the updated content back to file
  fs.writeFileSync(filePath, content, 'utf8');
  console.log(`Updated compatibility links for ${signPath}`);
}

// Update all zodiac sign pages except vaduren (already done manually)
zodiacSigns.forEach(sign => {
  if (sign !== 'vaduren') {
    updateCompatibilityLinks(sign);
  }
});

console.log('All compatibility links updated!');
