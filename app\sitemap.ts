import { MetadataRoute } from 'next'
import fs from 'fs'
import path from 'path'

// Base URL för webbplatsen
const baseUrl = 'https://horoskopet.nu'

// Funktion för att kontrollera om en page.tsx fil innehåller redirect
function isRedirectPage(filePath: string): boolean {
  try {
    if (!fs.existsSync(filePath)) {
      return false
    }

    const content = fs.readFileSync(filePath, 'utf8')
    // Kontrollera om filen innehåller redirect() funktionen och import från next/navigation
    const hasRedirect = content.includes('redirect(')
    const hasImport = content.includes('from "next/navigation"') || content.includes("from 'next/navigation'")
    return hasRedirect && hasImport
  } catch (error) {
    console.error(`Error reading file ${filePath}:`, error)
    return false
  }
}

// Funktion för att rekursivt hitta alla page.tsx filer (exkluderar redirect-sidor)
function getAllPages(dir: string, basePath: string = ''): string[] {
  const pages: string[] = []

  try {
    const items = fs.readdirSync(dir, { withFileTypes: true })

    for (const item of items) {
      const fullPath = path.join(dir, item.name)
      const urlPath = path.join(basePath, item.name).replace(/\\/g, '/')

      if (item.isDirectory()) {
        // Kolla om det finns en page.tsx i denna mapp
        const pagePath = path.join(fullPath, 'page.tsx')
        if (fs.existsSync(pagePath)) {
          // Kontrollera om det är en redirect-sida
          if (!isRedirectPage(pagePath)) {
            pages.push(urlPath === '' ? '/' : `/${urlPath}`)
          } else {
            console.log(`🔄 Exkluderar redirect-sida från sitemap: ${urlPath === '' ? '/' : `/${urlPath}`}`)
          }
        }

        // Rekursivt sök i undermappar
        pages.push(...getAllPages(fullPath, urlPath))
      }
    }
  } catch (error) {
    console.error(`Error reading directory ${dir}:`, error)
  }

  return pages
}

// Funktion för att filtrera bort dynamiska routes och oönskade sidor
function filterPages(pages: string[]): string[] {
  return pages.filter(page => {
    // Ta bort dynamiska routes (innehåller [])
    if (page.includes('[') || page.includes(']')) {
      return false
    }

    // Ta bort API routes
    if (page.includes('/api/')) {
      return false
    }

    // Kontrollera om det är en redirect-sida
    const appDir = path.join(process.cwd(), 'app')
    const pagePath = path.join(appDir, page === '/' ? 'page.tsx' : `${page}/page.tsx`)
    if (isRedirectPage(pagePath)) {
      console.log(`🔄 Filtrerar bort redirect-sida: ${page}`)
      return false
    }

    // Ta bort vissa specifika sidor som inte ska indexeras
    const excludedPaths = [
      '/sitemap', // HTML sitemap-sidan
      '/404',
      '/500',
      '/error'
    ]

    return !excludedPaths.includes(page)
  })
}

// Funktion för att bestämma prioritet baserat på sökväg
function getPriority(url: string): number {
  if (url === '/') return 1.0
  if (url.includes('/horoskop/dagens') || url.includes('/horoskop/veckans') || url.includes('/horoskop/manadens')) return 0.9
  if (url.includes('/stjarntecken/')) return 0.8
  if (url.includes('/horoskop')) return 0.8
  if (url.includes('/astrologi-lara')) return 0.7
  if (url.includes('/relationer')) return 0.7
  if (url.includes('/aktuellt')) return 0.6
  return 0.5
}

// Funktion för att bestämma uppdateringsfrekvens
function getChangeFrequency(url: string): 'always' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'never' {
  if (url.includes('/horoskop/dagens')) return 'daily'
  if (url.includes('/horoskop/veckans')) return 'weekly'
  if (url.includes('/horoskop/manadens')) return 'monthly'
  if (url.includes('/aktuellt')) return 'weekly'
  if (url === '/') return 'daily'
  if (url.includes('/horoskop')) return 'weekly'
  return 'monthly'
}

export default function sitemap(): MetadataRoute.Sitemap {
  // Hitta alla sidor i app-mappen
  const appDir = path.join(process.cwd(), 'app')
  const allPages = getAllPages(appDir)
  const filteredPages = filterPages(allPages)
  
  // Lägg till manuella sidor för dynamiska routes som vi vet finns
  const zodiacSigns = [
    'vaduren', 'oxen', 'tvillingarna', 'kraftan', 'lejonet', 'jungfrun',
    'vagen', 'skorpionen', 'skytten', 'stenbocken', 'vattumannen', 'fiskarna'
  ]
  
  // Lägg till alla stjärnteckens-sidor
  zodiacSigns.forEach(sign => {
    filteredPages.push(`/stjarntecken/${sign}`)
    filteredPages.push(`/horoskop/dagens/${sign}`)
    filteredPages.push(`/horoskop/veckans/${sign}`)
    filteredPages.push(`/horoskop/manadens/${sign}`)
    filteredPages.push(`/relationer/${sign}`)
  })
  
  // Lägg till kompatibilitetssidor
  zodiacSigns.forEach(sign1 => {
    zodiacSigns.forEach(sign2 => {
      filteredPages.push(`/relationer/${sign1}/${sign2}`)
    })
  })
  
  // Skapa sitemap-entries
  const sitemapEntries: MetadataRoute.Sitemap = filteredPages.map(page => ({
    url: `${baseUrl}${page}`,
    lastModified: new Date(),
    changeFrequency: getChangeFrequency(page),
    priority: getPriority(page),
  }))
  
  // Sortera efter prioritet (högst först)
  sitemapEntries.sort((a, b) => (b.priority || 0) - (a.priority || 0))
  
  return sitemapEntries
}
