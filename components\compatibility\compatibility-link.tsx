import Link from "next/link"
import { ReactNode } from "react"

interface CompatibilityLinkProps {
  currentSign: string
  targetSign: string
  children: ReactNode
  className?: string
}

/**
 * Component for creating compatibility links between zodiac signs.
 * Generates correct URLs for compatibility pages in format /relationer/[sign1]/[sign2]
 * 
 * @param currentSign - The current zodiac sign (path format, e.g., "vaduren")
 * @param targetSign - The target zodiac sign to check compatibility with (path format, e.g., "lejonet")
 * @param children - The content to display inside the link
 * @param className - Optional CSS classes for styling
 */
export function CompatibilityLink({ 
  currentSign, 
  targetSign, 
  children, 
  className = "" 
}: CompatibilityLinkProps) {
  // Generate the correct compatibility URL
  const compatibilityUrl = `/relationer/${currentSign}/${targetSign}`
  
  return (
    <Link 
      href={compatibilityUrl} 
      className={className}
    >
      {children}
    </Link>
  )
}

interface ZodiacNavigationLinkProps {
  currentSign: string
  targetSign: string
  targetSignName: string
  targetSignPath: string
  isCurrentSign?: boolean
  className?: string
  children: ReactNode
}

/**
 * Component for zodiac sign navigation that links to compatibility pages.
 * Used in the "Utforska Andra Stjärntecken" sections.
 */
export function ZodiacNavigationLink({
  currentSign,
  targetSign,
  targetSignName,
  targetSignPath,
  isCurrentSign = false,
  className = "",
  children
}: ZodiacNavigationLinkProps) {
  const compatibilityUrl = `/relationer/${currentSign}/${targetSignPath}`
  
  return (
    <Link
      href={compatibilityUrl}
      className={className}
    >
      {children}
    </Link>
  )
}
