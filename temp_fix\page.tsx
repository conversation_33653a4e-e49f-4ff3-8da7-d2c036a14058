import Link from "next/link"
import Image from "next/image"
import { ArrowLeft, ArrowRight, Calendar, Heart, Briefcase, Activity, Coins } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { Ta<PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { zodiacSigns } from "@/lib/zodiac-data"

// Get the next and previous signs
function getAdjacentSigns(currentPath: string) {
  const currentIndex = zodiacSigns.findIndex((sign) => sign.path === currentPath)
  const prevIndex = currentIndex > 0 ? currentIndex - 1 : zodiacSigns.length - 1
  const nextIndex = currentIndex < zodiacSigns.length - 1 ? currentIndex + 1 : 0

  return {
    prev: zodiacSigns[prevIndex],
    next: zodiacSigns[nextIndex],
  }
}

export default function ZodiacSignPage({ params }: { params: { sign: string } }) {
  const { sign } = params
  const currentSign = zodiacSigns.find((s) => s.path === sign)

  if (!currentSign) {
    return <div>Stjärntecken hittades inte</div>
  }

  const { prev, next } = getAdjacentSigns(sign)

  return (
    <div className="relative min-h-screen pt-28 pb-12">
      {/* Stjärnbakgrund overlay */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute inset-0 bg-[#0c0817] opacity-90"></div>
        <div className="stars-small opacity-30"></div>
      </div>

      <div className="container relative z-10 px-4 mx-auto">
        <div className="max-w-4xl mx-auto">
          <div className="flex flex-col md:flex-row md:items-center justify-between gap-4 mb-8">
            <h1 className="text-4xl md:text-5xl font-display font-bold text-white cosmic-title">{currentSign.name}</h1>
            <div className="flex gap-2">
              <Button variant="outline" size="sm" asChild className="bg-[#1a1333]/80 border-[#6e56cf]/30 text-white hover:bg-[#6e56cf]/20 hover:text-white hover:border-[#6e56cf]/50 transition-all duration-300">
                <Link href={`/stjarntecken/${prev.path}`} className="flex items-center gap-1">
                  <ArrowLeft className="h-4 w-4" />
                  {prev.name}
                </Link>
              </Button>
              <Button variant="outline" size="sm" asChild className="bg-[#1a1333]/80 border-[#6e56cf]/30 text-white hover:bg-[#6e56cf]/20 hover:text-white hover:border-[#6e56cf]/50 transition-all duration-300">
                <Link href={`/stjarntecken/${next.path}`} className="flex items-center gap-1">
                  {next.name}
                  <ArrowRight className="h-4 w-4" />
                </Link>
              </Button>
            </div>
          </div>

          <div className="grid md:grid-cols-3 gap-6 mb-8">
            <Card className="md:col-span-2 bg-gradient-to-br from-[#1a1333]/80 to-[#2d1d57]/80 border border-[#6e56cf]/20 shadow-lg shadow-primary/10 rounded-lg overflow-hidden">
              <CardContent className="p-6">
                <div className="prose max-w-none">
                  <p className="text-lg text-slate-300">{currentSign.description.split("\n\n")[0]}</p>

                  <Tabs defaultValue="overview" className="mt-6">
                    <TabsList className="grid grid-cols-4 bg-[#1e1b4b]/30 p-1 rounded-lg">
                      <TabsTrigger value="overview" className="data-[state=active]:bg-[#6e56cf] data-[state=active]:text-white">Översikt</TabsTrigger>
                      <TabsTrigger value="love" className="flex items-center gap-1 data-[state=active]:bg-[#6e56cf] data-[state=active]:text-white">
                        <Heart className="h-4 w-4" />
                        Kärlek
                      </TabsTrigger>
                      <TabsTrigger value="career" className="flex items-center gap-1 data-[state=active]:bg-[#6e56cf] data-[state=active]:text-white">
                        <Briefcase className="h-4 w-4" />
                        Karriär
                      </TabsTrigger>
                      <TabsTrigger value="health" className="flex items-center gap-1 data-[state=active]:bg-[#6e56cf] data-[state=active]:text-white">
                        <Activity className="h-4 w-4" />
                        Hälsa
                      </TabsTrigger>
                    </TabsList>
                    <TabsContent value="overview" className="mt-4">
                      {currentSign.description
                        .split("\n\n")
                        .slice(1)
                        .map((paragraph, index) => (
                          <p key={index} className="mb-4 text-slate-300">
                            {paragraph}
                          </p>
                        ))}
                    </TabsContent>
                    <TabsContent value="love" className="mt-4">
                      <h3 className="text-xl font-semibold mb-3 text-white">{currentSign.name} i kärlek och relationer</h3>
                      <p className="mb-4 text-slate-300">{currentSign.love || "Information om kärlek och relationer kommer snart."}</p>

                      <h4 className="font-medium mt-4 mb-2 text-white">Kompatibilitet</h4>
                      <div className="grid gap-4 sm:grid-cols-2">
                        <div className="bg-[#2d1d57]/50 p-4 rounded-lg border border-[#6e56cf]/20">
                          <h5 className="text-sm font-medium mb-1 text-white">Bäst matchning med:</h5>
                          <ul className="list-disc list-inside text-sm text-slate-300">
                            {currentSign.compatibility && currentSign.compatibility.best ? 
                              currentSign.compatibility.best.map((match, index) => (
                                <li key={index}>{match}</li>
                              )) : 
                              <li>Information kommer snart</li>
                            }
                          </ul>
                        </div>
                        <div className="bg-[#2d1d57]/50 p-4 rounded-lg border border-[#6e56cf]/20">
                          <h5 className="text-sm font-medium mb-1 text-white">Utmanande matchning med:</h5>
                          <ul className="list-disc list-inside text-sm text-slate-300">
                            {currentSign.compatibility && currentSign.compatibility.worst ? 
                              currentSign.compatibility.worst.map((match, index) => (
                                <li key={index}>{match}</li>
                              )) : 
                              <li>Information kommer snart</li>
                            }
                          </ul>
                        </div>
                      </div>

                      <div className="mt-4">
                        <Link href={`/relationer/${currentSign.path}`} className="text-[#a78bfa] hover:text-white transition-colors duration-300">
                          Läs mer om {currentSign.name} kompatibilitet →
                        </Link>
                      </div>
                    </TabsContent>
                    <TabsContent value="career" className="mt-4">
                      <h3 className="text-xl font-semibold mb-3 text-white">{currentSign.name} i arbetslivet</h3>
                      <p className="mb-4 text-slate-300">{currentSign.career || "Information om karriär kommer snart."}</p>

                      <h4 className="font-medium mt-4 mb-2 text-white">Styrkor i arbetslivet</h4>
                      <ul className="list-disc list-inside text-sm mb-4 text-slate-300">
                        {currentSign.strengths ?
                          currentSign.strengths
                            .split(", ")
                            .slice(0, 4)
                            .map((strength, index) => (
                              <li key={index}>{strength}</li>
                            )) :
                          <li>Information kommer snart</li>
                        }
                      </ul>

                      <h4 className="font-medium mt-4 mb-2 text-white">Utmaningar i arbetslivet</h4>
                      <ul className="list-disc list-inside text-sm text-slate-300">
                        {currentSign.weaknesses ?
                          currentSign.weaknesses
                            .split(", ")
                            .slice(0, 3)
                            .map((weakness, index) => (
                              <li key={index}>{weakness}</li>
                            )) :
                          <li>Information kommer snart</li>
                        }
                      </ul>
                    </TabsContent>
                    <TabsContent value="health" className="mt-4">
                      <h3 className="text-xl font-semibold mb-3 text-white">{currentSign.name} och hälsa</h3>
                      <p className="mb-4 text-slate-300">{currentSign.health || "Information om hälsa kommer snart."}</p>

                      <h4 className="font-medium mt-4 mb-2 text-white">Hälsotips för {currentSign.name}</h4>
                      <ul className="list-disc list-inside text-sm text-slate-300">
                        <li>Regelbunden motion som passar {currentSign.name}s temperament</li>
                        <li>Balanserad kost med fokus på näringsrika livsmedel</li>
                        <li>Stresshanteringstekniker anpassade för {currentSign.name}s personlighet</li>
                        <li>Tillräcklig vila och återhämtning</li>
                      </ul>
                    </TabsContent>
                  </Tabs>
                </div>
              </CardContent>
            </Card>

            <div className="space-y-6">
              <Card className="bg-gradient-to-br from-[#1a1333]/80 to-[#2d1d57]/80 border border-[#6e56cf]/20 shadow-lg shadow-primary/10 rounded-lg overflow-hidden">
                <CardContent className="p-6">
                  <div className="flex flex-col items-center text-center mb-4">
                    <div className="relative w-24 h-24 mb-3 rounded-full bg-gradient-to-br from-[#6e56cf]/30 to-[#a78bfa]/30 p-2 flex items-center justify-center shadow-lg shadow-primary/10">
                      <div className="w-full h-full relative overflow-hidden">
                        <Image
                          src={
                            currentSign.path === "vaduren" ? "/images/aries.jpg" :
                            currentSign.path === "oxen" ? "/images/taurus.jpg" :
                            currentSign.path === "tvillingarna" ? "/images/gemini.jpg" :
                            currentSign.path === "kraftan" ? "/images/cancer.jpg" :
                            currentSign.path === "lejonet" ? "/images/leo.jpg" :
                            currentSign.path === "jungfrun" ? "/images/virgo.jpg" :
                            currentSign.path === "vagen" ? "/images/libra.jpg" :
                            currentSign.path === "skorpionen" ? "/images/scorpio.jpg" :
                            currentSign.path === "skytten" ? "/images/sagittarius.jpg" :
                            currentSign.path === "stenbocken" ? "/images/capricorn.jpg" :
                            currentSign.path === "vattumannen" ? "/images/aquarius.jpg" :
                            currentSign.path === "fiskarna" ? "/images/pisces.jpg" :
                            "/images/zodiac.jpg"
                          }
                          alt={`${currentSign.name} symbol`}
                          fill
                          className="object-cover z-10 rounded-full"
                        />
                      </div>
                    </div>
                    <h2 className="text-xl font-bold text-white">{currentSign.name}</h2>
                    <p className="text-sm text-slate-300">{currentSign.dates}</p>
                  </div>

                  <Separator className="my-4 bg-[#6e56cf]/20" />

                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-sm text-slate-300">Element:</span>
                      <span className="text-sm font-medium text-white">{currentSign.element}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-slate-300">Styrande planet:</span>
                      <span className="text-sm font-medium text-white">{currentSign.ruling}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-slate-300">Kvalitet:</span>
                      <span className="text-sm font-medium text-white">{currentSign.quality}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-slate-300">Symbol:</span>
                      <span className="text-sm font-medium text-white">{currentSign.symbol}</span>
                    </div>
                  </div>

                  <Separator className="my-4 bg-[#6e56cf]/20" />

                  <div className="space-y-3">
                    <div>
                      <h3 className="text-sm font-medium mb-1 text-white">Styrkor:</h3>
                      <p className="text-sm text-slate-300">{currentSign.strengths}</p>
                    </div>
                    <div>
                      <h3 className="text-sm font-medium mb-1 text-white">Svagheter:</h3>
                      <p className="text-sm text-slate-300">{currentSign.weaknesses}</p>
                    </div>
                  </div>

                  <Separator className="my-4 bg-[#6e56cf]/20" />

                  <div className="space-y-3">
                    <div>
                      <h3 className="text-sm font-medium mb-1 text-white">Gillar:</h3>
                      <p className="text-sm text-slate-300">{currentSign.likes}</p>
                    </div>
                    <div>
                      <h3 className="text-sm font-medium mb-1 text-white">Ogillar:</h3>
                      <p className="text-sm text-slate-300">{currentSign.dislikes}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-gradient-to-br from-[#1a1333]/80 to-[#2d1d57]/80 border border-[#6e56cf]/20 shadow-lg shadow-primary/10 rounded-lg overflow-hidden">
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg text-white">Läs mer om {currentSign.name}</CardTitle>
                </CardHeader>
                <CardContent className="pb-6">
                  <ul className="space-y-2">
                    <li>
                      <Link
                        href={`/horoskop/dagens/${currentSign.path}`}
                        className="text-sm text-slate-300 hover:text-white flex items-center gap-2 transition-colors duration-300"
                      >
                        <Calendar className="h-4 w-4 text-[#a78bfa]" />
                        Dagens horoskop för {currentSign.name}
                      </Link>
                    </li>
                    <li>
                      <Link
                        href={`/relationer/${currentSign.path}`}
                        className="text-sm text-slate-300 hover:text-white flex items-center gap-2 transition-colors duration-300"
                      >
                        <Heart className="h-4 w-4 text-[#a78bfa]" />
                        {currentSign.name} i relationer
                      </Link>
                    </li>
                    <li>
                      <Link
                        href={`/astrologi-lara/planeter/${currentSign.ruling.toLowerCase()}`}
                        className="text-sm text-slate-300 hover:text-white flex items-center gap-2 transition-colors duration-300"
                      >
                        <Coins className="h-4 w-4 text-[#a78bfa]" />
                        Om planeten {currentSign.ruling}
                      </Link>
                    </li>
                  </ul>
                </CardContent>
              </Card>
            </div>
          </div>

          <div className="bg-gradient-to-br from-[#1a1333]/80 to-[#2d1d57]/80 border border-[#6e56cf]/20 shadow-lg shadow-primary/10 rounded-lg p-6 mb-8">
            <h3 className="text-xl font-semibold mb-4 text-white">Utforska andra stjärntecken</h3>
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-3">
              {zodiacSigns.map((zodiacSign) => (
                <Link
                  key={zodiacSign.path}
                  href={`/stjarntecken/${zodiacSign.path}`}
                  className={`flex flex-col items-center p-2 rounded-lg hover:bg-[#6e56cf]/20 transition-colors border ${
                    zodiacSign.path === sign 
                      ? "bg-[#6e56cf]/30 border-[#6e56cf]/50" 
                      : "bg-[#2d1d57]/30 border-[#6e56cf]/20"
                  }`}
                >
                  <div className="relative w-8 h-8 mb-1 bg-[#6e56cf]/20 rounded-full flex items-center justify-center">
                    <Image
                      src={`/abstract-geometric-shapes.png?height=32&width=32&query=${zodiacSign.name} zodiac symbol`}
                      alt={`${zodiacSign.name} symbol`}
                      fill
                      className="object-contain"
                    />
                  </div>
                  <span className="text-xs font-medium text-white">{zodiacSign.name}</span>
                </Link>
              ))}
            </div>
          </div>

          <div className="bg-gradient-to-br from-[#1a1333]/80 to-[#2d1d57]/80 border border-[#6e56cf]/20 shadow-lg shadow-primary/10 rounded-lg p-8 mb-8">
            <h2 className="text-2xl font-display font-bold mb-4 text-white">Fördjupa dig i astrologin</h2>
            <p className="text-slate-300 mb-6">
              För att få en djupare förståelse för hur {currentSign.name} påverkas av olika astrologiska faktorer,
              utforska våra artiklar om planeter, hus och aspekter. Ditt födelsehoroskop innehåller mycket mer information
              än bara ditt soltecken, och genom att lära dig mer om astrologins olika delar kan du få en mer nyanserad
              bild av din astrologiska profil.
            </p>

            <div className="grid gap-6 sm:grid-cols-2 md:grid-cols-3 mt-6">
              <Link
                href="/astrologi-lara/grunderna"
                className="p-4 border border-[#6e56cf]/30 rounded-lg bg-[#2d1d57]/30 hover:bg-[#6e56cf]/20 hover:border-[#6e56cf]/50 transition-all duration-300"
              >
                <h3 className="text-lg font-medium mb-2 text-white">Astrologins Grunder</h3>
                <p className="text-sm text-slate-300">
                  Lär dig om astrologins grundläggande principer och historia.
                </p>
              </Link>
              <Link
                href="/astrologi-lara/planeter"
                className="p-4 border border-[#6e56cf]/30 rounded-lg bg-[#2d1d57]/30 hover:bg-[#6e56cf]/20 hover:border-[#6e56cf]/50 transition-all duration-300"
              >
                <h3 className="text-lg font-medium mb-2 text-white">Planeterna</h3>
                <p className="text-sm text-slate-300">Utforska planeternas betydelse och påverkan i astrologin.</p>
              </Link>
              <Link 
                href="/astrologi-lara/husen" 
                className="p-4 border border-[#6e56cf]/30 rounded-lg bg-[#2d1d57]/30 hover:bg-[#6e56cf]/20 hover:border-[#6e56cf]/50 transition-all duration-300"
              >
                <h3 className="text-lg font-medium mb-2 text-white">De 12 Husen</h3>
                <p className="text-sm text-slate-300">
                  Förstå hur de astrologiska husen påverkar olika livsområden.
                </p>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
